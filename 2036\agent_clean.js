#!/usr/bin/env node

/**
 * 🤖 Advanced AI Agent - Complete Autonomous System
 * 🚀 Enhanced with streaming responses, comprehensive tools, and smart orchestration
 * 🧠 Built with memory, context awareness, and intelligent tool selection
 */

const { program } = require('commander');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { exec, spawn } = require('child_process');
const readline = require('readline');
const os = require('os');
const { promisify } = require('util');
const execAsync = promisify(exec);
const chalk = require('chalk');
const ora = require('ora');
const { v4: uuidv4 } = require('uuid');
const cheerio = require('cheerio');
const { createServer } = require('http-server');
const { JSDOM } = require('jsdom');
const glob = require('glob');
const crypto = require('crypto');

// Environment detection and configuration
const DEBUG = process.env.DEBUG === 'true';
const IS_WINDOWS = process.platform === 'win32';
const IS_MAC = process.platform === 'darwin';
const IS_LINUX = process.platform === 'linux';

// ═══════════════════════════════════════════════════════════════════════════════
// 🔧 CONFIGURATION & INITIALIZATION
// ═══════════════════════════════════════════════════════════════════════════════

const CONFIG_DIR = path.join(os.homedir(), '.ai-agent');
const DEFAULT_CONFIG = {
  apiKey: process.env.GEMINI_API_KEY || 'AIzaSyDOE7pTDMrVPGmNlkuCpgFav-85hjsrTtw',
  model: process.env.GEMINI_MODEL || 'gemini-2.0-flash-exp',
  temperature: 0.7,
  maxOutputTokens: 8192,
  topK: 40,
  topP: 0.95,
  historyPath: path.join(CONFIG_DIR, 'history.json'),
  configPath: path.join(CONFIG_DIR, 'config.json'),
  memoriesPath: path.join(CONFIG_DIR, 'memories'),
  contextPath: path.join(CONFIG_DIR, 'context'),
  tasksPath: path.join(CONFIG_DIR, 'tasks'),
  logsPath: path.join(CONFIG_DIR, 'logs'),
  maxHistorySize: 100,
  maxContextSize: 50,
  debug: DEBUG,
  autoSaveHistory: true,
  autoSaveContext: true,
  streamResponses: true,
  defaultTimeout: 60000,
  userPrompt: chalk.cyan('┌─ ') + chalk.bold.white('You') + chalk.cyan(' ─> '),
  agentPrompt: chalk.green('└─ ') + chalk.bold.green('Agent') + chalk.green(' ─> '),
  thinkingPrompt: chalk.yellow('🧠 ') + chalk.italic.yellow('Thinking...'),
  showTimestamps: true,
  showProgress: true
};

// Initialize configuration
let config = { ...DEFAULT_CONFIG };

// ═══════════════════════════════════════════════════════════════════════════════
// 🧠 ENHANCED MEMORY & CONTEXT SYSTEM
// ═══════════════════════════════════════════════════════════════════════════════

class AdvancedMemorySystem {
  constructor() {
    this.conversationMemory = [];
    this.taskMemory = new Map();
    this.contextMemory = new Map();
    this.longTermMemory = new Map();
    this.workingMemory = new Map();
    this.intentHistory = [];
    this.toolUsageStats = new Map();
    this.lastContext = null;
  }

  // Store conversation context with smart compression
  addConversationMemory(role, content, metadata = {}) {
    const memory = {
      id: uuidv4(),
      role,
      content,
      timestamp: Date.now(),
      metadata,
      importance: this.calculateImportance(content),
      embedding: this.generateSimpleEmbedding(content)
    };
    
    this.conversationMemory.push(memory);
    
    // Auto-compress if memory gets too large
    if (this.conversationMemory.length > config.maxHistorySize) {
      this.compressConversationMemory();
    }
  }

  // Calculate importance score for memory prioritization
  calculateImportance(content) {
    let score = 1;
    
    // Higher importance for code, errors, and commands
    if (content.includes('```') || content.includes('error') || content.includes('failed')) score += 2;
    if (content.includes('function') || content.includes('class') || content.includes('import')) score += 1;
    if (content.length > 500) score += 1;
    
    return Math.min(score, 5);
  }

  // Simple embedding generation for semantic search
  generateSimpleEmbedding(text) {
    const words = text.toLowerCase().split(/\s+/);
    const embedding = new Array(100).fill(0);
    
    words.forEach((word, index) => {
      const hash = this.simpleHash(word);
      embedding[hash % 100] += 1;
    });
    
    return embedding;
  }

  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  // Compress conversation memory by removing less important entries
  compressConversationMemory() {
    this.conversationMemory.sort((a, b) => b.importance - a.importance);
    this.conversationMemory = this.conversationMemory.slice(0, Math.floor(config.maxHistorySize * 0.8));
  }

  // Store task-specific memory
  addTaskMemory(taskId, data) {
    this.taskMemory.set(taskId, {
      ...data,
      timestamp: Date.now(),
      id: taskId
    });
  }

  // Store context for smart retrieval
  addContext(key, value, category = 'general') {
    this.contextMemory.set(key, {
      value,
      category,
      timestamp: Date.now(),
      accessCount: 0
    });
  }

  // Retrieve relevant context based on current situation
  getRelevantContext(query, limit = 5) {
    const queryEmbedding = this.generateSimpleEmbedding(query);
    const contexts = Array.from(this.contextMemory.entries());
    
    const scored = contexts.map(([key, context]) => {
      const similarity = this.cosineSimilarity(queryEmbedding, this.generateSimpleEmbedding(context.value));
      return { key, context, similarity };
    });
    
    return scored
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit)
      .map(item => ({ key: item.key, ...item.context }));
  }

  cosineSimilarity(a, b) {
    const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0);
    const magnitudeA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0));
    const magnitudeB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0));
    return dotProduct / (magnitudeA * magnitudeB) || 0;
  }

  // Save memory to disk
  async saveMemory() {
    try {
      const memoryData = {
        conversation: this.conversationMemory,
        tasks: Array.from(this.taskMemory.entries()),
        context: Array.from(this.contextMemory.entries()),
        longTerm: Array.from(this.longTermMemory.entries()),
        intentHistory: this.intentHistory,
        toolStats: Array.from(this.toolUsageStats.entries())
      };
      
      await fs.promises.writeFile(
        path.join(config.memoriesPath, 'advanced_memory.json'),
        JSON.stringify(memoryData, null, 2)
      );
    } catch (error) {
      logger.error('Failed to save memory:', error.message);
    }
  }

  // Load memory from disk
  async loadMemory() {
    try {
      const memoryPath = path.join(config.memoriesPath, 'advanced_memory.json');
      if (fs.existsSync(memoryPath)) {
        const memoryData = JSON.parse(await fs.promises.readFile(memoryPath, 'utf8'));
        
        this.conversationMemory = memoryData.conversation || [];
        this.taskMemory = new Map(memoryData.tasks || []);
        this.contextMemory = new Map(memoryData.context || []);
        this.longTermMemory = new Map(memoryData.longTerm || []);
        this.intentHistory = memoryData.intentHistory || [];
        this.toolUsageStats = new Map(memoryData.toolStats || []);
      }
    } catch (error) {
      logger.error('Failed to load memory:', error.message);
    }
  }
}

// Global memory system instance
const memorySystem = new AdvancedMemorySystem();

/**
 * Ensures all required directories exist
 */
function ensureDirectories() {
  const directories = [
    CONFIG_DIR,
    path.dirname(config.configPath),
    config.memoriesPath,
    config.contextPath,
    config.tasksPath,
    config.logsPath
  ];

  for (const dir of directories) {
    if (!fs.existsSync(dir)) {
      try {
        fs.mkdirSync(dir, { recursive: true });
        if (config.debug) {
          console.log(chalk.gray(`📁 Created directory: ${dir}`));
        }
      } catch (error) {
        console.error(chalk.red(`❌ Error creating directory ${dir}:`), error.message);
      }
    }
  }
}
