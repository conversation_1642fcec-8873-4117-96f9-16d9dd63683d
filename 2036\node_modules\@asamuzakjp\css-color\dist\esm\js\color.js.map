{"version": 3, "file": "color.js", "sources": ["../../../src/js/color.ts"], "sourcesContent": ["/**\n * color\n *\n * Ref: CSS Color Module Level 4\n *      Sample code for Color Conversions\n *      https://w3c.github.io/csswg-drafts/css-color-4/#color-conversion-code\n */\n\nimport {\n  CacheItem,\n  NullObject,\n  createCacheKey,\n  getCache,\n  setCache\n} from './cache';\nimport { isString } from './common';\nimport { interpolateHue, roundToPrecision } from './util';\nimport {\n  ColorChannels,\n  ComputedColorChannels,\n  Options,\n  MatchedRegExp,\n  SpecifiedColorChannels,\n  StringColorChannels,\n  StringColorSpacedChannels\n} from './typedef';\n\n/* constants */\nimport {\n  ANGLE,\n  CS_HUE_CAPT,\n  CS_MIX,\n  CS_RGB,\n  CS_XYZ,\n  FN_COLOR,\n  FN_MIX,\n  NONE,\n  NUM,\n  PCT,\n  SYN_COLOR_TYPE,\n  SYN_FN_COLOR,\n  SYN_HSL,\n  SYN_HSL_LV3,\n  SYN_LCH,\n  SYN_MIX,\n  SYN_MIX_CAPT,\n  SYN_MIX_PART,\n  SYN_MOD,\n  SYN_RGB_LV3,\n  VAL_COMP,\n  VAL_MIX,\n  VAL_SPEC\n} from './constant';\nconst NAMESPACE = 'color';\n\n/* numeric constants */\nconst PPTH = 0.001;\nconst HALF = 0.5;\nconst DUO = 2;\nconst TRIA = 3;\nconst QUAD = 4;\nconst OCT = 8;\nconst DEC = 10;\nconst DOZ = 12;\nconst HEX = 16;\nconst SEXA = 60;\nconst DEG_HALF = 180;\nconst DEG = 360;\nconst MAX_PCT = 100;\nconst MAX_RGB = 255;\nconst POW_SQR = 2;\nconst POW_CUBE = 3;\nconst POW_LINEAR = 2.4;\nconst LINEAR_COEF = 12.92;\nconst LINEAR_OFFSET = 0.055;\nconst LAB_L = 116;\nconst LAB_A = 500;\nconst LAB_B = 200;\nconst LAB_EPSILON = 216 / 24389;\nconst LAB_KAPPA = 24389 / 27;\n\n/* type definitions */\n/**\n * @type NumStrColorChannels - string or numeric color channels\n */\ntype NumStrColorChannels = [\n  x: number | string,\n  y: number | string,\n  z: number | string,\n  alpha: number | string\n];\n\n/**\n * @type TriColorChannels - color channels without alpha\n */\ntype TriColorChannels = [x: number, y: number, z: number];\n\n/**\n * @type ColorMatrix - color matrix\n */\ntype ColorMatrix = [\n  r1: TriColorChannels,\n  r2: TriColorChannels,\n  r3: TriColorChannels\n];\n\n/* white point */\nconst D50: TriColorChannels = [\n  0.3457 / 0.3585,\n  1.0,\n  (1.0 - 0.3457 - 0.3585) / 0.3585\n];\nconst MATRIX_D50_TO_D65: ColorMatrix = [\n  [0.955473421488075, -0.02309845494876471, 0.06325924320057072],\n  [-0.0283697093338637, 1.0099953980813041, 0.021041441191917323],\n  [0.012314014864481998, -0.020507649298898964, 1.330365926242124]\n];\nconst MATRIX_D65_TO_D50: ColorMatrix = [\n  [1.0479297925449969, 0.022946870601609652, -0.05019226628920524],\n  [0.02962780877005599, 0.9904344267538799, -0.017073799063418826],\n  [-0.009243040646204504, 0.015055191490298152, 0.7518742814281371]\n];\n\n/* color space */\nconst MATRIX_L_RGB_TO_XYZ: ColorMatrix = [\n  [506752 / 1228815, 87881 / 245763, 12673 / 70218],\n  [87098 / 409605, 175762 / 245763, 12673 / 175545],\n  [7918 / 409605, 87881 / 737289, 1001167 / 1053270]\n];\nconst MATRIX_XYZ_TO_L_RGB: ColorMatrix = [\n  [12831 / 3959, -329 / 214, -1974 / 3959],\n  [-851781 / 878810, 1648619 / 878810, 36519 / 878810],\n  [705 / 12673, -2585 / 12673, 705 / 667]\n];\nconst MATRIX_XYZ_TO_LMS: ColorMatrix = [\n  [0.819022437996703, 0.3619062600528904, -0.1288737815209879],\n  [0.0329836539323885, 0.9292868615863434, 0.0361446663506424],\n  [0.0481771893596242, 0.2642395317527308, 0.6335478284694309]\n];\nconst MATRIX_LMS_TO_XYZ: ColorMatrix = [\n  [1.2268798758459243, -0.5578149944602171, 0.2813910456659647],\n  [-0.0405757452148008, 1.112286803280317, -0.0717110580655164],\n  [-0.0763729366746601, -0.4214933324022432, 1.5869240198367816]\n];\nconst MATRIX_OKLAB_TO_LMS: ColorMatrix = [\n  [1.0, 0.3963377773761749, 0.2158037573099136],\n  [1.0, -0.1055613458156586, -0.0638541728258133],\n  [1.0, -0.0894841775298119, -1.2914855480194092]\n];\nconst MATRIX_LMS_TO_OKLAB: ColorMatrix = [\n  [0.210454268309314, 0.7936177747023054, -0.0040720430116193],\n  [1.9779985324311684, -2.4285922420485799, 0.450593709617411],\n  [0.0259040424655478, 0.7827717124575296, -0.8086757549230774]\n];\nconst MATRIX_P3_TO_XYZ: ColorMatrix = [\n  [608311 / 1250200, 189793 / 714400, 198249 / 1000160],\n  [35783 / 156275, 247089 / 357200, 198249 / 2500400],\n  [0 / 1, 32229 / 714400, 5220557 / 5000800]\n];\nconst MATRIX_REC2020_TO_XYZ: ColorMatrix = [\n  [63426534 / 99577255, 20160776 / 139408157, 47086771 / 278816314],\n  [26158966 / 99577255, 472592308 / 697040785, 8267143 / 139408157],\n  [0 / 1, 19567812 / 697040785, 295819943 / 278816314]\n];\nconst MATRIX_A98_TO_XYZ: ColorMatrix = [\n  [573536 / 994567, 263643 / 1420810, 187206 / 994567],\n  [591459 / 1989134, 6239551 / 9945670, 374412 / 4972835],\n  [53769 / 1989134, 351524 / 4972835, 4929758 / 4972835]\n];\nconst MATRIX_PROPHOTO_TO_XYZ_D50: ColorMatrix = [\n  [0.7977666449006423, 0.13518129740053308, 0.0313477341283922],\n  [0.2880748288194013, 0.711835234241873, 0.00008993693872564],\n  [0.0, 0.0, 0.8251046025104602]\n];\n\n/* regexp */\nconst REG_COLOR = new RegExp(`^(?:${SYN_COLOR_TYPE})$`);\nconst REG_CS_HUE = new RegExp(`^${CS_HUE_CAPT}$`);\nconst REG_CS_XYZ = /^xyz(?:-d(?:50|65))?$/;\nconst REG_CURRENT = /^currentColor$/i;\nconst REG_FN_COLOR = new RegExp(`^color\\\\(\\\\s*(${SYN_FN_COLOR})\\\\s*\\\\)$`);\nconst REG_HSL = new RegExp(`^hsla?\\\\(\\\\s*(${SYN_HSL}|${SYN_HSL_LV3})\\\\s*\\\\)$`);\nconst REG_HWB = new RegExp(`^hwb\\\\(\\\\s*(${SYN_HSL})\\\\s*\\\\)$`);\nconst REG_LAB = new RegExp(`^lab\\\\(\\\\s*(${SYN_MOD})\\\\s*\\\\)$`);\nconst REG_LCH = new RegExp(`^lch\\\\(\\\\s*(${SYN_LCH})\\\\s*\\\\)$`);\nconst REG_MIX = new RegExp(`^${SYN_MIX}$`);\nconst REG_MIX_CAPT = new RegExp(`^${SYN_MIX_CAPT}$`);\nconst REG_MIX_NEST = new RegExp(`${SYN_MIX}`, 'g');\nconst REG_OKLAB = new RegExp(`^oklab\\\\(\\\\s*(${SYN_MOD})\\\\s*\\\\)$`);\nconst REG_OKLCH = new RegExp(`^oklch\\\\(\\\\s*(${SYN_LCH})\\\\s*\\\\)$`);\nconst REG_SPEC = /^(?:specifi|comput)edValue$/;\n\n/**\n * named colors\n */\nexport const NAMED_COLORS = {\n  aliceblue: [0xf0, 0xf8, 0xff],\n  antiquewhite: [0xfa, 0xeb, 0xd7],\n  aqua: [0x00, 0xff, 0xff],\n  aquamarine: [0x7f, 0xff, 0xd4],\n  azure: [0xf0, 0xff, 0xff],\n  beige: [0xf5, 0xf5, 0xdc],\n  bisque: [0xff, 0xe4, 0xc4],\n  black: [0x00, 0x00, 0x00],\n  blanchedalmond: [0xff, 0xeb, 0xcd],\n  blue: [0x00, 0x00, 0xff],\n  blueviolet: [0x8a, 0x2b, 0xe2],\n  brown: [0xa5, 0x2a, 0x2a],\n  burlywood: [0xde, 0xb8, 0x87],\n  cadetblue: [0x5f, 0x9e, 0xa0],\n  chartreuse: [0x7f, 0xff, 0x00],\n  chocolate: [0xd2, 0x69, 0x1e],\n  coral: [0xff, 0x7f, 0x50],\n  cornflowerblue: [0x64, 0x95, 0xed],\n  cornsilk: [0xff, 0xf8, 0xdc],\n  crimson: [0xdc, 0x14, 0x3c],\n  cyan: [0x00, 0xff, 0xff],\n  darkblue: [0x00, 0x00, 0x8b],\n  darkcyan: [0x00, 0x8b, 0x8b],\n  darkgoldenrod: [0xb8, 0x86, 0x0b],\n  darkgray: [0xa9, 0xa9, 0xa9],\n  darkgreen: [0x00, 0x64, 0x00],\n  darkgrey: [0xa9, 0xa9, 0xa9],\n  darkkhaki: [0xbd, 0xb7, 0x6b],\n  darkmagenta: [0x8b, 0x00, 0x8b],\n  darkolivegreen: [0x55, 0x6b, 0x2f],\n  darkorange: [0xff, 0x8c, 0x00],\n  darkorchid: [0x99, 0x32, 0xcc],\n  darkred: [0x8b, 0x00, 0x00],\n  darksalmon: [0xe9, 0x96, 0x7a],\n  darkseagreen: [0x8f, 0xbc, 0x8f],\n  darkslateblue: [0x48, 0x3d, 0x8b],\n  darkslategray: [0x2f, 0x4f, 0x4f],\n  darkslategrey: [0x2f, 0x4f, 0x4f],\n  darkturquoise: [0x00, 0xce, 0xd1],\n  darkviolet: [0x94, 0x00, 0xd3],\n  deeppink: [0xff, 0x14, 0x93],\n  deepskyblue: [0x00, 0xbf, 0xff],\n  dimgray: [0x69, 0x69, 0x69],\n  dimgrey: [0x69, 0x69, 0x69],\n  dodgerblue: [0x1e, 0x90, 0xff],\n  firebrick: [0xb2, 0x22, 0x22],\n  floralwhite: [0xff, 0xfa, 0xf0],\n  forestgreen: [0x22, 0x8b, 0x22],\n  fuchsia: [0xff, 0x00, 0xff],\n  gainsboro: [0xdc, 0xdc, 0xdc],\n  ghostwhite: [0xf8, 0xf8, 0xff],\n  gold: [0xff, 0xd7, 0x00],\n  goldenrod: [0xda, 0xa5, 0x20],\n  gray: [0x80, 0x80, 0x80],\n  green: [0x00, 0x80, 0x00],\n  greenyellow: [0xad, 0xff, 0x2f],\n  grey: [0x80, 0x80, 0x80],\n  honeydew: [0xf0, 0xff, 0xf0],\n  hotpink: [0xff, 0x69, 0xb4],\n  indianred: [0xcd, 0x5c, 0x5c],\n  indigo: [0x4b, 0x00, 0x82],\n  ivory: [0xff, 0xff, 0xf0],\n  khaki: [0xf0, 0xe6, 0x8c],\n  lavender: [0xe6, 0xe6, 0xfa],\n  lavenderblush: [0xff, 0xf0, 0xf5],\n  lawngreen: [0x7c, 0xfc, 0x00],\n  lemonchiffon: [0xff, 0xfa, 0xcd],\n  lightblue: [0xad, 0xd8, 0xe6],\n  lightcoral: [0xf0, 0x80, 0x80],\n  lightcyan: [0xe0, 0xff, 0xff],\n  lightgoldenrodyellow: [0xfa, 0xfa, 0xd2],\n  lightgray: [0xd3, 0xd3, 0xd3],\n  lightgreen: [0x90, 0xee, 0x90],\n  lightgrey: [0xd3, 0xd3, 0xd3],\n  lightpink: [0xff, 0xb6, 0xc1],\n  lightsalmon: [0xff, 0xa0, 0x7a],\n  lightseagreen: [0x20, 0xb2, 0xaa],\n  lightskyblue: [0x87, 0xce, 0xfa],\n  lightslategray: [0x77, 0x88, 0x99],\n  lightslategrey: [0x77, 0x88, 0x99],\n  lightsteelblue: [0xb0, 0xc4, 0xde],\n  lightyellow: [0xff, 0xff, 0xe0],\n  lime: [0x00, 0xff, 0x00],\n  limegreen: [0x32, 0xcd, 0x32],\n  linen: [0xfa, 0xf0, 0xe6],\n  magenta: [0xff, 0x00, 0xff],\n  maroon: [0x80, 0x00, 0x00],\n  mediumaquamarine: [0x66, 0xcd, 0xaa],\n  mediumblue: [0x00, 0x00, 0xcd],\n  mediumorchid: [0xba, 0x55, 0xd3],\n  mediumpurple: [0x93, 0x70, 0xdb],\n  mediumseagreen: [0x3c, 0xb3, 0x71],\n  mediumslateblue: [0x7b, 0x68, 0xee],\n  mediumspringgreen: [0x00, 0xfa, 0x9a],\n  mediumturquoise: [0x48, 0xd1, 0xcc],\n  mediumvioletred: [0xc7, 0x15, 0x85],\n  midnightblue: [0x19, 0x19, 0x70],\n  mintcream: [0xf5, 0xff, 0xfa],\n  mistyrose: [0xff, 0xe4, 0xe1],\n  moccasin: [0xff, 0xe4, 0xb5],\n  navajowhite: [0xff, 0xde, 0xad],\n  navy: [0x00, 0x00, 0x80],\n  oldlace: [0xfd, 0xf5, 0xe6],\n  olive: [0x80, 0x80, 0x00],\n  olivedrab: [0x6b, 0x8e, 0x23],\n  orange: [0xff, 0xa5, 0x00],\n  orangered: [0xff, 0x45, 0x00],\n  orchid: [0xda, 0x70, 0xd6],\n  palegoldenrod: [0xee, 0xe8, 0xaa],\n  palegreen: [0x98, 0xfb, 0x98],\n  paleturquoise: [0xaf, 0xee, 0xee],\n  palevioletred: [0xdb, 0x70, 0x93],\n  papayawhip: [0xff, 0xef, 0xd5],\n  peachpuff: [0xff, 0xda, 0xb9],\n  peru: [0xcd, 0x85, 0x3f],\n  pink: [0xff, 0xc0, 0xcb],\n  plum: [0xdd, 0xa0, 0xdd],\n  powderblue: [0xb0, 0xe0, 0xe6],\n  purple: [0x80, 0x00, 0x80],\n  rebeccapurple: [0x66, 0x33, 0x99],\n  red: [0xff, 0x00, 0x00],\n  rosybrown: [0xbc, 0x8f, 0x8f],\n  royalblue: [0x41, 0x69, 0xe1],\n  saddlebrown: [0x8b, 0x45, 0x13],\n  salmon: [0xfa, 0x80, 0x72],\n  sandybrown: [0xf4, 0xa4, 0x60],\n  seagreen: [0x2e, 0x8b, 0x57],\n  seashell: [0xff, 0xf5, 0xee],\n  sienna: [0xa0, 0x52, 0x2d],\n  silver: [0xc0, 0xc0, 0xc0],\n  skyblue: [0x87, 0xce, 0xeb],\n  slateblue: [0x6a, 0x5a, 0xcd],\n  slategray: [0x70, 0x80, 0x90],\n  slategrey: [0x70, 0x80, 0x90],\n  snow: [0xff, 0xfa, 0xfa],\n  springgreen: [0x00, 0xff, 0x7f],\n  steelblue: [0x46, 0x82, 0xb4],\n  tan: [0xd2, 0xb4, 0x8c],\n  teal: [0x00, 0x80, 0x80],\n  thistle: [0xd8, 0xbf, 0xd8],\n  tomato: [0xff, 0x63, 0x47],\n  turquoise: [0x40, 0xe0, 0xd0],\n  violet: [0xee, 0x82, 0xee],\n  wheat: [0xf5, 0xde, 0xb3],\n  white: [0xff, 0xff, 0xff],\n  whitesmoke: [0xf5, 0xf5, 0xf5],\n  yellow: [0xff, 0xff, 0x00],\n  yellowgreen: [0x9a, 0xcd, 0x32]\n} as const satisfies {\n  [key: string]: TriColorChannels;\n};\n\n/**\n * cache invalid color value\n * @param key - cache key\n * @param nullable - is nullable\n * @returns cached value\n */\nexport const cacheInvalidColorValue = (\n  cacheKey: string,\n  format: string,\n  nullable: boolean = false\n): SpecifiedColorChannels | string | NullObject => {\n  if (format === VAL_SPEC) {\n    const res = '';\n    setCache(cacheKey, res);\n    return res;\n  }\n  if (nullable) {\n    setCache(cacheKey, null);\n    return new NullObject();\n  }\n  const res: SpecifiedColorChannels = ['rgb', 0, 0, 0, 0];\n  setCache(cacheKey, res);\n  return res;\n};\n\n/**\n * resolve invalid color value\n * @param format - output format\n * @param nullable - is nullable\n * @returns resolved value\n */\nexport const resolveInvalidColorValue = (\n  format: string,\n  nullable: boolean = false\n): SpecifiedColorChannels | string | NullObject => {\n  switch (format) {\n    case 'hsl':\n    case 'hwb':\n    case VAL_MIX: {\n      return new NullObject();\n    }\n    case VAL_SPEC: {\n      return '';\n    }\n    default: {\n      if (nullable) {\n        return new NullObject();\n      }\n      return ['rgb', 0, 0, 0, 0] as SpecifiedColorChannels;\n    }\n  }\n};\n\n/**\n * validate color components\n * @param arr - color components\n * @param [opt] - options\n * @param [opt.alpha] - alpha channel\n * @param [opt.minLength] - min length\n * @param [opt.maxLength] - max length\n * @param [opt.minRange] - min range\n * @param [opt.maxRange] - max range\n * @param [opt.validateRange] - validate range\n * @returns result - validated color components\n */\nexport const validateColorComponents = (\n  arr: ColorChannels | TriColorChannels,\n  opt: {\n    alpha?: boolean;\n    minLength?: number;\n    maxLength?: number;\n    minRange?: number;\n    maxRange?: number;\n    validateRange?: boolean;\n  } = {}\n): ColorChannels | TriColorChannels => {\n  if (!Array.isArray(arr)) {\n    throw new TypeError(`${arr} is not an array.`);\n  }\n  const {\n    alpha = false,\n    minLength = TRIA,\n    maxLength = QUAD,\n    minRange = 0,\n    maxRange = 1,\n    validateRange = true\n  } = opt;\n  if (!Number.isFinite(minLength)) {\n    throw new TypeError(`${minLength} is not a number.`);\n  }\n  if (!Number.isFinite(maxLength)) {\n    throw new TypeError(`${maxLength} is not a number.`);\n  }\n  if (!Number.isFinite(minRange)) {\n    throw new TypeError(`${minRange} is not a number.`);\n  }\n  if (!Number.isFinite(maxRange)) {\n    throw new TypeError(`${maxRange} is not a number.`);\n  }\n  const l = arr.length;\n  if (l < minLength || l > maxLength) {\n    throw new Error(`Unexpected array length ${l}.`);\n  }\n  let i = 0;\n  while (i < l) {\n    const v = arr[i] as number;\n    if (!Number.isFinite(v)) {\n      throw new TypeError(`${v} is not a number.`);\n    } else if (i < TRIA && validateRange && (v < minRange || v > maxRange)) {\n      throw new RangeError(`${v} is not between ${minRange} and ${maxRange}.`);\n    } else if (i === TRIA && (v < 0 || v > 1)) {\n      throw new RangeError(`${v} is not between 0 and 1.`);\n    }\n    i++;\n  }\n  if (alpha && l === TRIA) {\n    arr.push(1);\n  }\n  return arr;\n};\n\n/**\n * transform matrix\n * @param mtx - 3 * 3 matrix\n * @param vct - vector\n * @param [skip] - skip validate\n * @returns TriColorChannels - [p1, p2, p3]\n */\nexport const transformMatrix = (\n  mtx: ColorMatrix,\n  vct: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  if (!Array.isArray(mtx)) {\n    throw new TypeError(`${mtx} is not an array.`);\n  } else if (mtx.length !== TRIA) {\n    throw new Error(`Unexpected array length ${mtx.length}.`);\n  } else if (!skip) {\n    for (let i of mtx) {\n      i = validateColorComponents(i as TriColorChannels, {\n        maxLength: TRIA,\n        validateRange: false\n      }) as TriColorChannels;\n    }\n  }\n  const [[r1c1, r1c2, r1c3], [r2c1, r2c2, r2c3], [r3c1, r3c2, r3c3]] = mtx;\n  let v1, v2, v3;\n  if (skip) {\n    [v1, v2, v3] = vct;\n  } else {\n    [v1, v2, v3] = validateColorComponents(vct, {\n      maxLength: TRIA,\n      validateRange: false\n    });\n  }\n  const p1 = r1c1 * v1 + r1c2 * v2 + r1c3 * v3;\n  const p2 = r2c1 * v1 + r2c2 * v2 + r2c3 * v3;\n  const p3 = r3c1 * v1 + r3c2 * v2 + r3c3 * v3;\n  return [p1, p2, p3];\n};\n\n/**\n * normalize color components\n * @param colorA - color components [v1, v2, v3, v4]\n * @param colorB - color components [v1, v2, v3, v4]\n * @param [skip] - skip validate\n * @returns result - [colorA, colorB]\n */\nexport const normalizeColorComponents = (\n  colorA: [number | string, number | string, number | string, number | string],\n  colorB: [number | string, number | string, number | string, number | string],\n  skip: boolean = false\n): [ColorChannels, ColorChannels] => {\n  if (!Array.isArray(colorA)) {\n    throw new TypeError(`${colorA} is not an array.`);\n  } else if (colorA.length !== QUAD) {\n    throw new Error(`Unexpected array length ${colorA.length}.`);\n  }\n  if (!Array.isArray(colorB)) {\n    throw new TypeError(`${colorB} is not an array.`);\n  } else if (colorB.length !== QUAD) {\n    throw new Error(`Unexpected array length ${colorB.length}.`);\n  }\n  let i = 0;\n  while (i < QUAD) {\n    if (colorA[i] === NONE && colorB[i] === NONE) {\n      colorA[i] = 0;\n      colorB[i] = 0;\n    } else if (colorA[i] === NONE) {\n      colorA[i] = colorB[i] as number;\n    } else if (colorB[i] === NONE) {\n      colorB[i] = colorA[i] as number;\n    }\n    i++;\n  }\n  if (skip) {\n    return [colorA as ColorChannels, colorB as ColorChannels];\n  }\n  const validatedColorA = validateColorComponents(colorA as ColorChannels, {\n    minLength: QUAD,\n    validateRange: false\n  });\n  const validatedColorB = validateColorComponents(colorB as ColorChannels, {\n    minLength: QUAD,\n    validateRange: false\n  });\n  return [validatedColorA as ColorChannels, validatedColorB as ColorChannels];\n};\n\n/**\n * number to hex string\n * @param value - numeric value\n * @returns hex string\n */\nexport const numberToHexString = (value: number): string => {\n  if (!Number.isFinite(value)) {\n    throw new TypeError(`${value} is not a number.`);\n  } else {\n    value = Math.round(value);\n    if (value < 0 || value > MAX_RGB) {\n      throw new RangeError(`${value} is not between 0 and ${MAX_RGB}.`);\n    }\n  }\n  let hex = value.toString(HEX);\n  if (hex.length === 1) {\n    hex = `0${hex}`;\n  }\n  return hex;\n};\n\n/**\n * angle to deg\n * @param angle\n * @returns deg: 0..360\n */\nexport const angleToDeg = (angle: string): number => {\n  if (isString(angle)) {\n    angle = angle.trim();\n  } else {\n    throw new TypeError(`${angle} is not a string.`);\n  }\n  const GRAD = DEG / 400;\n  const RAD = DEG / (Math.PI * DUO);\n  const reg = new RegExp(`^(${NUM})(${ANGLE})?$`);\n  if (!reg.test(angle)) {\n    throw new SyntaxError(`Invalid property value: ${angle}`);\n  }\n  const [, value, unit] = angle.match(reg) as MatchedRegExp;\n  let deg;\n  switch (unit) {\n    case 'grad':\n      deg = parseFloat(value) * GRAD;\n      break;\n    case 'rad':\n      deg = parseFloat(value) * RAD;\n      break;\n    case 'turn':\n      deg = parseFloat(value) * DEG;\n      break;\n    default:\n      deg = parseFloat(value);\n  }\n  deg %= DEG;\n  if (deg < 0) {\n    deg += DEG;\n  } else if (Object.is(deg, -0)) {\n    deg = 0;\n  }\n  return deg;\n};\n\n/**\n * parse alpha\n * @param [alpha] - alpha value\n * @returns alpha: 0..1\n */\nexport const parseAlpha = (alpha: string = ''): number => {\n  if (isString(alpha)) {\n    alpha = alpha.trim();\n    if (!alpha) {\n      alpha = '1';\n    } else if (alpha === NONE) {\n      alpha = '0';\n    } else {\n      let a;\n      if (alpha.endsWith('%')) {\n        a = parseFloat(alpha) / MAX_PCT;\n      } else {\n        a = parseFloat(alpha);\n      }\n      if (!Number.isFinite(a)) {\n        throw new TypeError(`${a} is not a finite number.`);\n      }\n      if (a < PPTH) {\n        alpha = '0';\n      } else if (a > 1) {\n        alpha = '1';\n      } else {\n        alpha = a.toFixed(TRIA);\n      }\n    }\n  } else {\n    alpha = '1';\n  }\n  return parseFloat(alpha);\n};\n\n/**\n * parse hex alpha\n * @param value - alpha value in hex string\n * @returns alpha: 0..1\n */\nexport const parseHexAlpha = (value: string): number => {\n  if (isString(value)) {\n    if (value === '') {\n      throw new SyntaxError('Invalid property value: (empty string)');\n    }\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  let alpha = parseInt(value, HEX);\n  if (alpha <= 0) {\n    return 0;\n  }\n  if (alpha >= MAX_RGB) {\n    return 1;\n  }\n  const alphaMap = new Map();\n  for (let i = 1; i < MAX_PCT; i++) {\n    alphaMap.set(Math.round((i * MAX_RGB) / MAX_PCT), i);\n  }\n  if (alphaMap.has(alpha)) {\n    alpha = alphaMap.get(alpha) / MAX_PCT;\n  } else {\n    alpha = Math.round(alpha / MAX_RGB / PPTH) * PPTH;\n  }\n  return parseFloat(alpha.toFixed(TRIA));\n};\n\n/**\n * transform rgb to linear rgb\n * @param rgb - [r, g, b] r|g|b: 0..255\n * @param [skip] - skip validate\n * @returns TriColorChannels - [r, g, b] r|g|b: 0..1\n */\nexport const transformRgbToLinearRgb = (\n  rgb: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  let rr, gg, bb;\n  if (skip) {\n    [rr, gg, bb] = rgb;\n  } else {\n    [rr, gg, bb] = validateColorComponents(rgb, {\n      maxLength: TRIA,\n      maxRange: MAX_RGB\n    });\n  }\n  let r = rr / MAX_RGB;\n  let g = gg / MAX_RGB;\n  let b = bb / MAX_RGB;\n  const COND_POW = 0.04045;\n  if (r > COND_POW) {\n    r = Math.pow((r + LINEAR_OFFSET) / (1 + LINEAR_OFFSET), POW_LINEAR);\n  } else {\n    r /= LINEAR_COEF;\n  }\n  if (g > COND_POW) {\n    g = Math.pow((g + LINEAR_OFFSET) / (1 + LINEAR_OFFSET), POW_LINEAR);\n  } else {\n    g /= LINEAR_COEF;\n  }\n  if (b > COND_POW) {\n    b = Math.pow((b + LINEAR_OFFSET) / (1 + LINEAR_OFFSET), POW_LINEAR);\n  } else {\n    b /= LINEAR_COEF;\n  }\n  return [r, g, b];\n};\n\n/**\n * transform rgb to xyz\n * @param rgb - [r, g, b] r|g|b: 0..255\n * @param [skip] - skip validate\n * @returns TriColorChannels - [x, y, z]\n */\nexport const transformRgbToXyz = (\n  rgb: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  if (!skip) {\n    rgb = validateColorComponents(rgb, {\n      maxLength: TRIA,\n      maxRange: MAX_RGB\n    }) as TriColorChannels;\n  }\n  rgb = transformRgbToLinearRgb(rgb, true);\n  const xyz = transformMatrix(MATRIX_L_RGB_TO_XYZ, rgb, true);\n  return xyz;\n};\n\n/**\n * transform rgb to xyz-d50\n * @param rgb - [r, g, b] r|g|b: 0..255 alpha: 0..1\n * @returns TriColorChannels - [x, y, z]\n */\nexport const transformRgbToXyzD50 = (\n  rgb: TriColorChannels\n): TriColorChannels => {\n  let xyz = transformRgbToXyz(rgb);\n  xyz = transformMatrix(MATRIX_D65_TO_D50, xyz, true);\n  return xyz;\n};\n\n/**\n * transform linear rgb to rgb\n * @param rgb - [r, g, b] r|g|b: 0..1\n * @param [round] - round result\n * @returns TriColorChannels - [r, g, b] r|g|b: 0..255\n */\nexport const transformLinearRgbToRgb = (\n  rgb: TriColorChannels,\n  round: boolean = false\n): TriColorChannels => {\n  let [r, g, b] = validateColorComponents(rgb, {\n    maxLength: TRIA\n  });\n  const COND_POW = 809 / 258400;\n  if (r > COND_POW) {\n    r = Math.pow(r, 1 / POW_LINEAR) * (1 + LINEAR_OFFSET) - LINEAR_OFFSET;\n  } else {\n    r *= LINEAR_COEF;\n  }\n  r *= MAX_RGB;\n  if (g > COND_POW) {\n    g = Math.pow(g, 1 / POW_LINEAR) * (1 + LINEAR_OFFSET) - LINEAR_OFFSET;\n  } else {\n    g *= LINEAR_COEF;\n  }\n  g *= MAX_RGB;\n  if (b > COND_POW) {\n    b = Math.pow(b, 1 / POW_LINEAR) * (1 + LINEAR_OFFSET) - LINEAR_OFFSET;\n  } else {\n    b *= LINEAR_COEF;\n  }\n  b *= MAX_RGB;\n  return [\n    round ? Math.round(r) : r,\n    round ? Math.round(g) : g,\n    round ? Math.round(b) : b\n  ];\n};\n\n/**\n * transform xyz to rgb\n * @param xyz - [x, y, z]\n * @param [skip] - skip validate\n * @returns TriColorChannels - [r, g, b] r|g|b: 0..255\n */\nexport const transformXyzToRgb = (\n  xyz: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  if (!skip) {\n    xyz = validateColorComponents(xyz, {\n      maxLength: TRIA,\n      validateRange: false\n    }) as TriColorChannels;\n  }\n  let [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, xyz, true);\n  [r, g, b] = transformLinearRgbToRgb(\n    [\n      Math.min(Math.max(r, 0), 1),\n      Math.min(Math.max(g, 0), 1),\n      Math.min(Math.max(b, 0), 1)\n    ],\n    true\n  );\n  return [r, g, b];\n};\n\n/**\n * transform xyz to xyz-d50\n * @param xyz - [x, y, z]\n * @returns TriColorChannels - [x, y, z]\n */\nexport const transformXyzToXyzD50 = (\n  xyz: TriColorChannels\n): TriColorChannels => {\n  xyz = validateColorComponents(xyz, {\n    maxLength: TRIA,\n    validateRange: false\n  }) as TriColorChannels;\n  xyz = transformMatrix(MATRIX_D65_TO_D50, xyz, true);\n  return xyz;\n};\n\n/**\n * transform xyz to hsl\n * @param xyz - [x, y, z]\n * @param [skip] - skip validate\n * @returns TriColorChannels - [h, s, l]\n */\nexport const transformXyzToHsl = (\n  xyz: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  const [rr, gg, bb] = transformXyzToRgb(xyz, skip);\n  const r = rr / MAX_RGB;\n  const g = gg / MAX_RGB;\n  const b = bb / MAX_RGB;\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  const d = max - min;\n  const l = (max + min) * HALF * MAX_PCT;\n  let h, s;\n  if (Math.round(l) === 0 || Math.round(l) === MAX_PCT) {\n    h = 0;\n    s = 0;\n  } else {\n    s = (d / (1 - Math.abs(max + min - 1))) * MAX_PCT;\n    if (s === 0) {\n      h = 0;\n    } else {\n      switch (max) {\n        case r:\n          h = (g - b) / d;\n          break;\n        case g:\n          h = (b - r) / d + DUO;\n          break;\n        case b:\n        default:\n          h = (r - g) / d + QUAD;\n          break;\n      }\n      h = (h * SEXA) % DEG;\n      if (h < 0) {\n        h += DEG;\n      }\n    }\n  }\n  return [h, s, l];\n};\n\n/**\n * transform xyz to hwb\n * @param xyz - [x, y, z]\n * @param [skip] - skip validate\n * @returns TriColorChannels - [h, w, b]\n */\nexport const transformXyzToHwb = (\n  xyz: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  const [r, g, b] = transformXyzToRgb(xyz, skip);\n  const wh = Math.min(r, g, b) / MAX_RGB;\n  const bk = 1 - Math.max(r, g, b) / MAX_RGB;\n  let h;\n  if (wh + bk === 1) {\n    h = 0;\n  } else {\n    [h] = transformXyzToHsl(xyz);\n  }\n  return [h, wh * MAX_PCT, bk * MAX_PCT];\n};\n\n/**\n * transform xyz to oklab\n * @param xyz - [x, y, z]\n * @param [skip] - skip validate\n * @returns TriColorChannels - [l, a, b]\n */\nexport const transformXyzToOklab = (\n  xyz: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  if (!skip) {\n    xyz = validateColorComponents(xyz, {\n      maxLength: TRIA,\n      validateRange: false\n    }) as TriColorChannels;\n  }\n  const lms = transformMatrix(MATRIX_XYZ_TO_LMS, xyz, true);\n  const xyzLms = lms.map(c => Math.cbrt(c)) as TriColorChannels;\n  let [l, a, b] = transformMatrix(MATRIX_LMS_TO_OKLAB, xyzLms, true);\n  l = Math.min(Math.max(l, 0), 1);\n  const lPct = Math.round(parseFloat(l.toFixed(QUAD)) * MAX_PCT);\n  if (lPct === 0 || lPct === MAX_PCT) {\n    a = 0;\n    b = 0;\n  }\n  return [l, a, b];\n};\n\n/**\n * transform xyz to oklch\n * @param xyz - [x, y, z]\n * @param [skip] - skip validate\n * @returns TriColorChannels - [l, c, h]\n */\nexport const transformXyzToOklch = (\n  xyz: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  const [l, a, b] = transformXyzToOklab(xyz, skip);\n  let c, h;\n  const lPct = Math.round(parseFloat(l.toFixed(QUAD)) * MAX_PCT);\n  if (lPct === 0 || lPct === MAX_PCT) {\n    c = 0;\n    h = 0;\n  } else {\n    c = Math.max(Math.sqrt(Math.pow(a, POW_SQR) + Math.pow(b, POW_SQR)), 0);\n    if (parseFloat(c.toFixed(QUAD)) === 0) {\n      h = 0;\n    } else {\n      h = (Math.atan2(b, a) * DEG_HALF) / Math.PI;\n      if (h < 0) {\n        h += DEG;\n      }\n    }\n  }\n  return [l, c, h];\n};\n\n/**\n * transform xyz D50 to rgb\n * @param xyz - [x, y, z]\n * @param [skip] - skip validate\n * @returns TriColorChannels - [r, g, b] r|g|b: 0..255\n */\nexport const transformXyzD50ToRgb = (\n  xyz: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  if (!skip) {\n    xyz = validateColorComponents(xyz, {\n      maxLength: TRIA,\n      validateRange: false\n    }) as TriColorChannels;\n  }\n  const xyzD65 = transformMatrix(MATRIX_D50_TO_D65, xyz, true);\n  const rgb = transformXyzToRgb(xyzD65, true);\n  return rgb;\n};\n\n/**\n * transform xyz-d50 to lab\n * @param xyz - [x, y, z]\n * @param [skip] - skip validate\n * @returns TriColorChannels - [l, a, b]\n */\nexport const transformXyzD50ToLab = (\n  xyz: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  if (!skip) {\n    xyz = validateColorComponents(xyz, {\n      maxLength: TRIA,\n      validateRange: false\n    }) as TriColorChannels;\n  }\n  const xyzD50 = xyz.map((val, i) => val / (D50[i] as number));\n  const [f0, f1, f2] = xyzD50.map(val =>\n    val > LAB_EPSILON ? Math.cbrt(val) : (val * LAB_KAPPA + HEX) / LAB_L\n  ) as TriColorChannels;\n  const l = Math.min(Math.max(LAB_L * f1 - HEX, 0), MAX_PCT);\n  let a, b;\n  if (l === 0 || l === MAX_PCT) {\n    a = 0;\n    b = 0;\n  } else {\n    a = (f0 - f1) * LAB_A;\n    b = (f1 - f2) * LAB_B;\n  }\n  return [l, a, b];\n};\n\n/**\n * transform xyz-d50 to lch\n * @param xyz - [x, y, z]\n * @param [skip] - skip validate\n * @returns TriColorChannels - [l, c, h]\n */\nexport const transformXyzD50ToLch = (\n  xyz: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  const [l, a, b] = transformXyzD50ToLab(xyz, skip);\n  let c, h;\n  if (l === 0 || l === MAX_PCT) {\n    c = 0;\n    h = 0;\n  } else {\n    c = Math.max(Math.sqrt(Math.pow(a, POW_SQR) + Math.pow(b, POW_SQR)), 0);\n    h = (Math.atan2(b, a) * DEG_HALF) / Math.PI;\n    if (h < 0) {\n      h += DEG;\n    }\n  }\n  return [l, c, h];\n};\n\n/**\n * convert rgb to hex color\n * @param rgb - [r, g, b, alpha] r|g|b: 0..255 alpha: 0..1\n * @returns hex color\n */\nexport const convertRgbToHex = (rgb: ColorChannels): string => {\n  const [r, g, b, alpha] = validateColorComponents(rgb, {\n    alpha: true,\n    maxRange: MAX_RGB\n  }) as ColorChannels;\n  const rr = numberToHexString(r);\n  const gg = numberToHexString(g);\n  const bb = numberToHexString(b);\n  const aa = numberToHexString(alpha * MAX_RGB);\n  let hex;\n  if (aa === 'ff') {\n    hex = `#${rr}${gg}${bb}`;\n  } else {\n    hex = `#${rr}${gg}${bb}${aa}`;\n  }\n  return hex;\n};\n\n/**\n * convert linear rgb to hex color\n * @param rgb - [r, g, b, alpha] r|g|b|alpha: 0..1\n * @param [skip] - skip validate\n * @returns hex color\n */\nexport const convertLinearRgbToHex = (\n  rgb: ColorChannels,\n  skip: boolean = false\n): string => {\n  let r, g, b, alpha;\n  if (skip) {\n    [r, g, b, alpha] = rgb;\n  } else {\n    [r, g, b, alpha] = validateColorComponents(rgb, {\n      minLength: QUAD\n    }) as ColorChannels;\n  }\n  [r, g, b] = transformLinearRgbToRgb([r, g, b], true);\n  const rr = numberToHexString(r);\n  const gg = numberToHexString(g);\n  const bb = numberToHexString(b);\n  const aa = numberToHexString(alpha * MAX_RGB);\n  let hex;\n  if (aa === 'ff') {\n    hex = `#${rr}${gg}${bb}`;\n  } else {\n    hex = `#${rr}${gg}${bb}${aa}`;\n  }\n  return hex;\n};\n\n/**\n * convert xyz to hex color\n * @param xyz - [x, y, z, alpha]\n * @returns hex color\n */\nexport const convertXyzToHex = (xyz: ColorChannels): string => {\n  const [x, y, z, alpha] = validateColorComponents(xyz, {\n    minLength: QUAD,\n    validateRange: false\n  }) as ColorChannels;\n  const [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, [x, y, z], true);\n  const hex = convertLinearRgbToHex(\n    [\n      Math.min(Math.max(r, 0), 1),\n      Math.min(Math.max(g, 0), 1),\n      Math.min(Math.max(b, 0), 1),\n      alpha\n    ],\n    true\n  );\n  return hex;\n};\n\n/**\n * convert xyz D50 to hex color\n * @param xyz - [x, y, z, alpha]\n * @returns hex color\n */\nexport const convertXyzD50ToHex = (xyz: ColorChannels): string => {\n  const [x, y, z, alpha] = validateColorComponents(xyz, {\n    minLength: QUAD,\n    validateRange: false\n  }) as ColorChannels;\n  const xyzD65 = transformMatrix(MATRIX_D50_TO_D65, [x, y, z], true);\n  const [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, xyzD65, true);\n  const hex = convertLinearRgbToHex([\n    Math.min(Math.max(r, 0), 1),\n    Math.min(Math.max(g, 0), 1),\n    Math.min(Math.max(b, 0), 1),\n    alpha\n  ]);\n  return hex;\n};\n\n/**\n * convert hex color to rgb\n * @param value - hex color value\n * @returns ColorChannels - [r, g, b, alpha] r|g|b: 0..255 alpha: 0..1\n */\nexport const convertHexToRgb = (value: string): ColorChannels => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  if (\n    !(\n      /^#[\\da-f]{6}$/.test(value) ||\n      /^#[\\da-f]{3}$/.test(value) ||\n      /^#[\\da-f]{8}$/.test(value) ||\n      /^#[\\da-f]{4}$/.test(value)\n    )\n  ) {\n    throw new SyntaxError(`Invalid property value: ${value}`);\n  }\n  const arr: number[] = [];\n  if (/^#[\\da-f]{3}$/.test(value)) {\n    const [, r, g, b] = value.match(\n      /^#([\\da-f])([\\da-f])([\\da-f])$/\n    ) as MatchedRegExp;\n    arr.push(\n      parseInt(`${r}${r}`, HEX),\n      parseInt(`${g}${g}`, HEX),\n      parseInt(`${b}${b}`, HEX),\n      1\n    );\n  } else if (/^#[\\da-f]{4}$/.test(value)) {\n    const [, r, g, b, alpha] = value.match(\n      /^#([\\da-f])([\\da-f])([\\da-f])([\\da-f])$/\n    ) as MatchedRegExp;\n    arr.push(\n      parseInt(`${r}${r}`, HEX),\n      parseInt(`${g}${g}`, HEX),\n      parseInt(`${b}${b}`, HEX),\n      parseHexAlpha(`${alpha}${alpha}`)\n    );\n  } else if (/^#[\\da-f]{8}$/.test(value)) {\n    const [, r, g, b, alpha] = value.match(\n      /^#([\\da-f]{2})([\\da-f]{2})([\\da-f]{2})([\\da-f]{2})$/\n    ) as MatchedRegExp;\n    arr.push(\n      parseInt(r, HEX),\n      parseInt(g, HEX),\n      parseInt(b, HEX),\n      parseHexAlpha(alpha)\n    );\n  } else {\n    const [, r, g, b] = value.match(\n      /^#([\\da-f]{2})([\\da-f]{2})([\\da-f]{2})$/\n    ) as MatchedRegExp;\n    arr.push(parseInt(r, HEX), parseInt(g, HEX), parseInt(b, HEX), 1);\n  }\n  return arr as ColorChannels;\n};\n\n/**\n * convert hex color to linear rgb\n * @param value - hex color value\n * @returns ColorChannels - [r, g, b, alpha] r|g|b|alpha: 0..1\n */\nexport const convertHexToLinearRgb = (value: string): ColorChannels => {\n  const [rr, gg, bb, alpha] = convertHexToRgb(value);\n  const [r, g, b] = transformRgbToLinearRgb([rr, gg, bb], true);\n  return [r, g, b, alpha];\n};\n\n/**\n * convert hex color to xyz\n * @param value - hex color value\n * @returns ColorChannels - [x, y, z, alpha]\n */\nexport const convertHexToXyz = (value: string): ColorChannels => {\n  const [r, g, b, alpha] = convertHexToLinearRgb(value);\n  const [x, y, z] = transformMatrix(MATRIX_L_RGB_TO_XYZ, [r, g, b], true);\n  return [x, y, z, alpha];\n};\n\n/**\n * parse rgb()\n * @param value - rgb color value\n * @param [opt] - options\n * @returns parsed color - ['rgb', r, g, b, alpha], '(empty)', NullObject\n */\nexport const parseRgb = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '', nullable = false } = opt;\n  const reg = new RegExp(`^rgba?\\\\(\\\\s*(${SYN_MOD}|${SYN_RGB_LV3})\\\\s*\\\\)$`);\n  if (!reg.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const [, val] = value.match(reg) as MatchedRegExp;\n  const [v1, v2, v3, v4 = ''] = val\n    .replace(/[,/]/g, ' ')\n    .split(/\\s+/) as StringColorChannels;\n  let r, g, b;\n  if (v1 === NONE) {\n    r = 0;\n  } else {\n    if (v1.endsWith('%')) {\n      r = (parseFloat(v1) * MAX_RGB) / MAX_PCT;\n    } else {\n      r = parseFloat(v1);\n    }\n    r = Math.min(Math.max(roundToPrecision(r, OCT), 0), MAX_RGB);\n  }\n  if (v2 === NONE) {\n    g = 0;\n  } else {\n    if (v2.endsWith('%')) {\n      g = (parseFloat(v2) * MAX_RGB) / MAX_PCT;\n    } else {\n      g = parseFloat(v2);\n    }\n    g = Math.min(Math.max(roundToPrecision(g, OCT), 0), MAX_RGB);\n  }\n  if (v3 === NONE) {\n    b = 0;\n  } else {\n    if (v3.endsWith('%')) {\n      b = (parseFloat(v3) * MAX_RGB) / MAX_PCT;\n    } else {\n      b = parseFloat(v3);\n    }\n    b = Math.min(Math.max(roundToPrecision(b, OCT), 0), MAX_RGB);\n  }\n  const alpha = parseAlpha(v4);\n  return ['rgb', r, g, b, format === VAL_MIX && v4 === NONE ? NONE : alpha];\n};\n\n/**\n * parse hsl()\n * @param value - hsl color value\n * @param [opt] - options\n * @returns parsed color - ['rgb', r, g, b, alpha], '(empty)', NullObject\n */\nexport const parseHsl = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '', nullable = false } = opt;\n  if (!REG_HSL.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const [, val] = value.match(REG_HSL) as MatchedRegExp;\n  const [v1, v2, v3, v4 = ''] = val\n    .replace(/[,/]/g, ' ')\n    .split(/\\s+/) as StringColorChannels;\n  let h, s, l;\n  if (v1 === NONE) {\n    h = 0;\n  } else {\n    h = angleToDeg(v1);\n  }\n  if (v2 === NONE) {\n    s = 0;\n  } else {\n    s = Math.min(Math.max(parseFloat(v2), 0), MAX_PCT);\n  }\n  if (v3 === NONE) {\n    l = 0;\n  } else {\n    l = Math.min(Math.max(parseFloat(v3), 0), MAX_PCT);\n  }\n  const alpha = parseAlpha(v4);\n  if (format === 'hsl') {\n    return [\n      format,\n      v1 === NONE ? v1 : h,\n      v2 === NONE ? v2 : s,\n      v3 === NONE ? v3 : l,\n      v4 === NONE ? v4 : alpha\n    ];\n  }\n  h = (h / DEG) * DOZ;\n  l /= MAX_PCT;\n  const sa = (s / MAX_PCT) * Math.min(l, 1 - l);\n  const rk = h % DOZ;\n  const gk = (8 + h) % DOZ;\n  const bk = (4 + h) % DOZ;\n  const r = l - sa * Math.max(-1, Math.min(rk - TRIA, TRIA ** POW_SQR - rk, 1));\n  const g = l - sa * Math.max(-1, Math.min(gk - TRIA, TRIA ** POW_SQR - gk, 1));\n  const b = l - sa * Math.max(-1, Math.min(bk - TRIA, TRIA ** POW_SQR - bk, 1));\n  return [\n    'rgb',\n    Math.min(Math.max(roundToPrecision(r * MAX_RGB, OCT), 0), MAX_RGB),\n    Math.min(Math.max(roundToPrecision(g * MAX_RGB, OCT), 0), MAX_RGB),\n    Math.min(Math.max(roundToPrecision(b * MAX_RGB, OCT), 0), MAX_RGB),\n    alpha\n  ];\n};\n\n/**\n * parse hwb()\n * @param value - hwb color value\n * @param [opt] - options\n * @returns parsed color - ['rgb', r, g, b, alpha], '(empty)', NullObject\n */\nexport const parseHwb = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '', nullable = false } = opt;\n  if (!REG_HWB.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const [, val] = value.match(REG_HWB) as MatchedRegExp;\n  const [v1, v2, v3, v4 = ''] = val\n    .replace('/', ' ')\n    .split(/\\s+/) as StringColorChannels;\n  let h, wh, bk;\n  if (v1 === NONE) {\n    h = 0;\n  } else {\n    h = angleToDeg(v1);\n  }\n  if (v2 === NONE) {\n    wh = 0;\n  } else {\n    wh = Math.min(Math.max(parseFloat(v2), 0), MAX_PCT) / MAX_PCT;\n  }\n  if (v3 === NONE) {\n    bk = 0;\n  } else {\n    bk = Math.min(Math.max(parseFloat(v3), 0), MAX_PCT) / MAX_PCT;\n  }\n  const alpha = parseAlpha(v4);\n  if (format === 'hwb') {\n    return [\n      format,\n      v1 === NONE ? v1 : h,\n      v2 === NONE ? v2 : wh * MAX_PCT,\n      v3 === NONE ? v3 : bk * MAX_PCT,\n      v4 === NONE ? v4 : alpha\n    ];\n  }\n  if (wh + bk >= 1) {\n    const v = roundToPrecision((wh / (wh + bk)) * MAX_RGB, OCT);\n    return ['rgb', v, v, v, alpha];\n  }\n  const factor = (1 - wh - bk) / MAX_RGB;\n  let [, r, g, b] = parseHsl(`hsl(${h} 100 50)`) as ComputedColorChannels;\n  r = roundToPrecision((r * factor + wh) * MAX_RGB, OCT);\n  g = roundToPrecision((g * factor + wh) * MAX_RGB, OCT);\n  b = roundToPrecision((b * factor + wh) * MAX_RGB, OCT);\n  return [\n    'rgb',\n    Math.min(Math.max(r, 0), MAX_RGB),\n    Math.min(Math.max(g, 0), MAX_RGB),\n    Math.min(Math.max(b, 0), MAX_RGB),\n    alpha\n  ];\n};\n\n/**\n * parse lab()\n * @param value - lab color value\n * @param [opt] - options\n * @returns parsed color\n *   - [xyz-d50, x, y, z, alpha], ['lab', l, a, b, alpha], '(empty)', NullObject\n */\nexport const parseLab = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '', nullable = false } = opt;\n  if (!REG_LAB.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const COEF_PCT = 1.25;\n  const COND_POW = 8;\n  const [, val] = value.match(REG_LAB) as MatchedRegExp;\n  const [v1, v2, v3, v4 = ''] = val\n    .replace('/', ' ')\n    .split(/\\s+/) as StringColorChannels;\n  let l, a, b;\n  if (v1 === NONE) {\n    l = 0;\n  } else {\n    if (v1.endsWith('%')) {\n      l = parseFloat(v1);\n      if (l > MAX_PCT) {\n        l = MAX_PCT;\n      }\n    } else {\n      l = parseFloat(v1);\n    }\n    if (l < 0) {\n      l = 0;\n    }\n  }\n  if (v2 === NONE) {\n    a = 0;\n  } else {\n    a = v2.endsWith('%') ? parseFloat(v2) * COEF_PCT : parseFloat(v2);\n  }\n  if (v3 === NONE) {\n    b = 0;\n  } else {\n    b = v3.endsWith('%') ? parseFloat(v3) * COEF_PCT : parseFloat(v3);\n  }\n  const alpha = parseAlpha(v4);\n  if (REG_SPEC.test(format)) {\n    return [\n      'lab',\n      v1 === NONE ? v1 : roundToPrecision(l, HEX),\n      v2 === NONE ? v2 : roundToPrecision(a, HEX),\n      v3 === NONE ? v3 : roundToPrecision(b, HEX),\n      v4 === NONE ? v4 : alpha\n    ];\n  }\n  const fl = (l + HEX) / LAB_L;\n  const fa = a / LAB_A + fl;\n  const fb = fl - b / LAB_B;\n  const powFl = Math.pow(fl, POW_CUBE);\n  const powFa = Math.pow(fa, POW_CUBE);\n  const powFb = Math.pow(fb, POW_CUBE);\n  const xyz = [\n    powFa > LAB_EPSILON ? powFa : (fa * LAB_L - HEX) / LAB_KAPPA,\n    l > COND_POW ? powFl : l / LAB_KAPPA,\n    powFb > LAB_EPSILON ? powFb : (fb * LAB_L - HEX) / LAB_KAPPA\n  ];\n  const [x, y, z] = xyz.map(\n    (val, i) => val * (D50[i] as number)\n  ) as TriColorChannels;\n  return [\n    'xyz-d50',\n    roundToPrecision(x, HEX),\n    roundToPrecision(y, HEX),\n    roundToPrecision(z, HEX),\n    alpha\n  ];\n};\n\n/**\n * parse lch()\n * @param value - lch color value\n * @param [opt] - options\n * @returns parsed color\n *   - ['xyz-d50', x, y, z, alpha], ['lch', l, c, h, alpha]\n *   - '(empty)', NullObject\n */\nexport const parseLch = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '', nullable = false } = opt;\n  if (!REG_LCH.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const COEF_PCT = 1.5;\n  const [, val] = value.match(REG_LCH) as MatchedRegExp;\n  const [v1, v2, v3, v4 = ''] = val\n    .replace('/', ' ')\n    .split(/\\s+/) as StringColorChannels;\n  let l, c, h;\n  if (v1 === NONE) {\n    l = 0;\n  } else {\n    l = parseFloat(v1);\n    if (l < 0) {\n      l = 0;\n    }\n  }\n  if (v2 === NONE) {\n    c = 0;\n  } else {\n    c = v2.endsWith('%') ? parseFloat(v2) * COEF_PCT : parseFloat(v2);\n  }\n  if (v3 === NONE) {\n    h = 0;\n  } else {\n    h = angleToDeg(v3);\n  }\n  const alpha = parseAlpha(v4);\n  if (REG_SPEC.test(format)) {\n    return [\n      'lch',\n      v1 === NONE ? v1 : roundToPrecision(l, HEX),\n      v2 === NONE ? v2 : roundToPrecision(c, HEX),\n      v3 === NONE ? v3 : roundToPrecision(h, HEX),\n      v4 === NONE ? v4 : alpha\n    ];\n  }\n  const a = c * Math.cos((h * Math.PI) / DEG_HALF);\n  const b = c * Math.sin((h * Math.PI) / DEG_HALF);\n  const [, x, y, z] = parseLab(`lab(${l} ${a} ${b})`) as ComputedColorChannels;\n  return [\n    'xyz-d50',\n    roundToPrecision(x, HEX),\n    roundToPrecision(y, HEX),\n    roundToPrecision(z, HEX),\n    alpha as number\n  ];\n};\n\n/**\n * parse oklab()\n * @param value - oklab color value\n * @param [opt] - options\n * @returns parsed color\n *   - ['xyz-d65', x, y, z, alpha], ['oklab', l, a, b, alpha]\n *   - '(empty)', NullObject\n */\nexport const parseOklab = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '', nullable = false } = opt;\n  if (!REG_OKLAB.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const COEF_PCT = 0.4;\n  const [, val] = value.match(REG_OKLAB) as MatchedRegExp;\n  const [v1, v2, v3, v4 = ''] = val\n    .replace('/', ' ')\n    .split(/\\s+/) as StringColorChannels;\n  let l, a, b;\n  if (v1 === NONE) {\n    l = 0;\n  } else {\n    l = v1.endsWith('%') ? parseFloat(v1) / MAX_PCT : parseFloat(v1);\n    if (l < 0) {\n      l = 0;\n    }\n  }\n  if (v2 === NONE) {\n    a = 0;\n  } else if (v2.endsWith('%')) {\n    a = (parseFloat(v2) * COEF_PCT) / MAX_PCT;\n  } else {\n    a = parseFloat(v2);\n  }\n  if (v3 === NONE) {\n    b = 0;\n  } else if (v3.endsWith('%')) {\n    b = (parseFloat(v3) * COEF_PCT) / MAX_PCT;\n  } else {\n    b = parseFloat(v3);\n  }\n  const alpha = parseAlpha(v4);\n  if (REG_SPEC.test(format)) {\n    return [\n      'oklab',\n      v1 === NONE ? v1 : roundToPrecision(l, HEX),\n      v2 === NONE ? v2 : roundToPrecision(a, HEX),\n      v3 === NONE ? v3 : roundToPrecision(b, HEX),\n      v4 === NONE ? v4 : alpha\n    ];\n  }\n  const lms = transformMatrix(MATRIX_OKLAB_TO_LMS, [l, a, b]);\n  const xyzLms = lms.map(c => Math.pow(c, POW_CUBE)) as TriColorChannels;\n  const [x, y, z] = transformMatrix(MATRIX_LMS_TO_XYZ, xyzLms, true);\n  return [\n    'xyz-d65',\n    roundToPrecision(x, HEX),\n    roundToPrecision(y, HEX),\n    roundToPrecision(z, HEX),\n    alpha as number\n  ];\n};\n\n/**\n * parse oklch()\n * @param value - oklch color value\n * @param [opt] - options\n * @returns parsed color\n *   - ['xyz-d65', x, y, z, alpha], ['oklch', l, c, h, alpha]\n *   - '(empty)', NullObject\n */\nexport const parseOklch = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '', nullable = false } = opt;\n  if (!REG_OKLCH.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const COEF_PCT = 0.4;\n  const [, val] = value.match(REG_OKLCH) as MatchedRegExp;\n  const [v1, v2, v3, v4 = ''] = val\n    .replace('/', ' ')\n    .split(/\\s+/) as StringColorChannels;\n  let l, c, h;\n  if (v1 === NONE) {\n    l = 0;\n  } else {\n    l = v1.endsWith('%') ? parseFloat(v1) / MAX_PCT : parseFloat(v1);\n    if (l < 0) {\n      l = 0;\n    }\n  }\n  if (v2 === NONE) {\n    c = 0;\n  } else {\n    if (v2.endsWith('%')) {\n      c = (parseFloat(v2) * COEF_PCT) / MAX_PCT;\n    } else {\n      c = parseFloat(v2);\n    }\n    if (c < 0) {\n      c = 0;\n    }\n  }\n  if (v3 === NONE) {\n    h = 0;\n  } else {\n    h = angleToDeg(v3);\n  }\n  const alpha = parseAlpha(v4);\n  if (REG_SPEC.test(format)) {\n    return [\n      'oklch',\n      v1 === NONE ? v1 : roundToPrecision(l, HEX),\n      v2 === NONE ? v2 : roundToPrecision(c, HEX),\n      v3 === NONE ? v3 : roundToPrecision(h, HEX),\n      v4 === NONE ? v4 : alpha\n    ];\n  }\n  const a = c * Math.cos((h * Math.PI) / DEG_HALF);\n  const b = c * Math.sin((h * Math.PI) / DEG_HALF);\n  const lms = transformMatrix(MATRIX_OKLAB_TO_LMS, [l, a, b]);\n  const xyzLms = lms.map(cc => Math.pow(cc, POW_CUBE)) as TriColorChannels;\n  const [x, y, z] = transformMatrix(MATRIX_LMS_TO_XYZ, xyzLms, true);\n  return [\n    'xyz-d65',\n    roundToPrecision(x, HEX),\n    roundToPrecision(y, HEX),\n    roundToPrecision(z, HEX),\n    alpha\n  ];\n};\n\n/**\n * parse color()\n * @param value - color function value\n * @param [opt] - options\n * @returns parsed color\n *   - ['xyz-(d50|d65)', x, y, z, alpha], [cs, r, g, b, alpha]\n *   - '(empty)', NullObject\n */\nexport const parseColorFunc = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { colorSpace = '', d50 = false, format = '', nullable = false } = opt;\n  if (!REG_FN_COLOR.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const [, val] = value.match(REG_FN_COLOR) as MatchedRegExp;\n  let [cs, v1, v2, v3, v4 = ''] = val\n    .replace('/', ' ')\n    .split(/\\s+/) as StringColorSpacedChannels;\n  let r, g, b;\n  if (cs === 'xyz') {\n    cs = 'xyz-d65';\n  }\n  if (v1 === NONE) {\n    r = 0;\n  } else {\n    r = v1.endsWith('%') ? parseFloat(v1) / MAX_PCT : parseFloat(v1);\n  }\n  if (v2 === NONE) {\n    g = 0;\n  } else {\n    g = v2.endsWith('%') ? parseFloat(v2) / MAX_PCT : parseFloat(v2);\n  }\n  if (v3 === NONE) {\n    b = 0;\n  } else {\n    b = v3.endsWith('%') ? parseFloat(v3) / MAX_PCT : parseFloat(v3);\n  }\n  const alpha = parseAlpha(v4);\n  if (REG_SPEC.test(format) || (format === VAL_MIX && cs === colorSpace)) {\n    return [\n      cs,\n      v1 === NONE ? v1 : roundToPrecision(r, DEC),\n      v2 === NONE ? v2 : roundToPrecision(g, DEC),\n      v3 === NONE ? v3 : roundToPrecision(b, DEC),\n      v4 === NONE ? v4 : alpha\n    ];\n  }\n  let x = 0;\n  let y = 0;\n  let z = 0;\n  // srgb-linear\n  if (cs === 'srgb-linear') {\n    [x, y, z] = transformMatrix(MATRIX_L_RGB_TO_XYZ, [r, g, b]);\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n    // display-p3\n  } else if (cs === 'display-p3') {\n    const linearRgb = transformRgbToLinearRgb([\n      r * MAX_RGB,\n      g * MAX_RGB,\n      b * MAX_RGB\n    ]);\n    [x, y, z] = transformMatrix(MATRIX_P3_TO_XYZ, linearRgb);\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n    // rec2020\n  } else if (cs === 'rec2020') {\n    const ALPHA = 1.09929682680944;\n    const BETA = 0.018053968510807;\n    const REC_COEF = 0.45;\n    const rgb = [r, g, b].map(c => {\n      let cl;\n      if (c < BETA * REC_COEF * DEC) {\n        cl = c / (REC_COEF * DEC);\n      } else {\n        cl = Math.pow((c + ALPHA - 1) / ALPHA, 1 / REC_COEF);\n      }\n      return cl;\n    }) as TriColorChannels;\n    [x, y, z] = transformMatrix(MATRIX_REC2020_TO_XYZ, rgb);\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n    // a98-rgb\n  } else if (cs === 'a98-rgb') {\n    const POW_A98 = 563 / 256;\n    const rgb = [r, g, b].map(c => {\n      const cl = Math.pow(c, POW_A98);\n      return cl;\n    }) as TriColorChannels;\n    [x, y, z] = transformMatrix(MATRIX_A98_TO_XYZ, rgb);\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n    // prophoto-rgb\n  } else if (cs === 'prophoto-rgb') {\n    const POW_PROPHOTO = 1.8;\n    const rgb = [r, g, b].map(c => {\n      let cl;\n      if (c > 1 / (HEX * DUO)) {\n        cl = Math.pow(c, POW_PROPHOTO);\n      } else {\n        cl = c / HEX;\n      }\n      return cl;\n    }) as TriColorChannels;\n    [x, y, z] = transformMatrix(MATRIX_PROPHOTO_TO_XYZ_D50, rgb);\n    if (!d50) {\n      [x, y, z] = transformMatrix(MATRIX_D50_TO_D65, [x, y, z], true);\n    }\n    // xyz, xyz-d50, xyz-d65\n  } else if (/^xyz(?:-d(?:50|65))?$/.test(cs)) {\n    [x, y, z] = [r, g, b];\n    if (cs === 'xyz-d50') {\n      if (!d50) {\n        [x, y, z] = transformMatrix(MATRIX_D50_TO_D65, [x, y, z]);\n      }\n    } else if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n    // srgb\n  } else {\n    [x, y, z] = transformRgbToXyz([r * MAX_RGB, g * MAX_RGB, b * MAX_RGB]);\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n  }\n  return [\n    d50 ? 'xyz-d50' : 'xyz-d65',\n    roundToPrecision(x, HEX),\n    roundToPrecision(y, HEX),\n    roundToPrecision(z, HEX),\n    format === VAL_MIX && v4 === NONE ? v4 : alpha\n  ];\n};\n\n/**\n * parse color value\n * @param value - CSS color value\n * @param [opt] - options\n * @returns parsed color\n *   - ['xyz-(d50|d65)', x, y, z, alpha], ['rgb', r, g, b, alpha]\n *   - value, '(empty)', NullObject\n */\nexport const parseColorValue = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { d50 = false, format = '', nullable = false } = opt;\n  if (!REG_COLOR.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  let x = 0;\n  let y = 0;\n  let z = 0;\n  let alpha = 0;\n  // complement currentcolor as a missing color\n  if (REG_CURRENT.test(value)) {\n    if (format === VAL_COMP) {\n      return ['rgb', 0, 0, 0, 0];\n    }\n    if (format === VAL_SPEC) {\n      return value;\n    }\n    // named-color\n  } else if (/^[a-z]+$/.test(value)) {\n    if (Object.prototype.hasOwnProperty.call(NAMED_COLORS, value)) {\n      if (format === VAL_SPEC) {\n        return value;\n      }\n      const [r, g, b] = NAMED_COLORS[\n        value as keyof typeof NAMED_COLORS\n      ] as TriColorChannels;\n      alpha = 1;\n      if (format === VAL_COMP) {\n        return ['rgb', r, g, b, alpha];\n      }\n      [x, y, z] = transformRgbToXyz([r, g, b], true);\n      if (d50) {\n        [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n      }\n    } else {\n      switch (format) {\n        case VAL_COMP: {\n          if (nullable && value !== 'transparent') {\n            return new NullObject();\n          }\n          return ['rgb', 0, 0, 0, 0];\n        }\n        case VAL_SPEC: {\n          if (value === 'transparent') {\n            return value;\n          }\n          return '';\n        }\n        case VAL_MIX: {\n          if (value === 'transparent') {\n            return ['rgb', 0, 0, 0, 0];\n          }\n          return new NullObject();\n        }\n        default:\n      }\n    }\n    // hex-color\n  } else if (value[0] === '#') {\n    if (REG_SPEC.test(format)) {\n      const rgb = convertHexToRgb(value);\n      return ['rgb', ...rgb];\n    }\n    [x, y, z, alpha] = convertHexToXyz(value);\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n    // lab()\n  } else if (value.startsWith('lab')) {\n    if (REG_SPEC.test(format)) {\n      return parseLab(value, opt);\n    }\n    [, x, y, z, alpha] = parseLab(value) as ComputedColorChannels;\n    if (!d50) {\n      [x, y, z] = transformMatrix(MATRIX_D50_TO_D65, [x, y, z], true);\n    }\n    // lch()\n  } else if (value.startsWith('lch')) {\n    if (REG_SPEC.test(format)) {\n      return parseLch(value, opt);\n    }\n    [, x, y, z, alpha] = parseLch(value) as ComputedColorChannels;\n    if (!d50) {\n      [x, y, z] = transformMatrix(MATRIX_D50_TO_D65, [x, y, z], true);\n    }\n    // oklab()\n  } else if (value.startsWith('oklab')) {\n    if (REG_SPEC.test(format)) {\n      return parseOklab(value, opt);\n    }\n    [, x, y, z, alpha] = parseOklab(value) as ComputedColorChannels;\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n    // oklch()\n  } else if (value.startsWith('oklch')) {\n    if (REG_SPEC.test(format)) {\n      return parseOklch(value, opt);\n    }\n    [, x, y, z, alpha] = parseOklch(value) as ComputedColorChannels;\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n  } else {\n    let r, g, b;\n    // hsl()\n    if (value.startsWith('hsl')) {\n      [, r, g, b, alpha] = parseHsl(value) as ComputedColorChannels;\n      // hwb()\n    } else if (value.startsWith('hwb')) {\n      [, r, g, b, alpha] = parseHwb(value) as ComputedColorChannels;\n      // rgb()\n    } else {\n      [, r, g, b, alpha] = parseRgb(value, opt) as ComputedColorChannels;\n    }\n    if (REG_SPEC.test(format)) {\n      return ['rgb', Math.round(r), Math.round(g), Math.round(b), alpha];\n    }\n    [x, y, z] = transformRgbToXyz([r, g, b]);\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n  }\n  return [\n    d50 ? 'xyz-d50' : 'xyz-d65',\n    roundToPrecision(x, HEX),\n    roundToPrecision(y, HEX),\n    roundToPrecision(z, HEX),\n    alpha\n  ];\n};\n\n/**\n * resolve color value\n * @param value - CSS color value\n * @param [opt] - options\n * @returns resolved color\n *   - [cs, v1, v2, v3, alpha], value, '(empty)', NullObject\n */\nexport const resolveColorValue = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { colorSpace = '', format = '', nullable = false } = opt;\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'resolveColorValue',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    const cachedItem = cachedResult.item;\n    if (isString(cachedItem)) {\n      return cachedItem as string;\n    }\n    return cachedItem as SpecifiedColorChannels;\n  }\n  if (!REG_COLOR.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      setCache(cacheKey, null);\n      return res;\n    }\n    setCache(cacheKey, res);\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  let cs = '';\n  let r = 0;\n  let g = 0;\n  let b = 0;\n  let alpha = 0;\n  // complement currentcolor as a missing color\n  if (REG_CURRENT.test(value)) {\n    if (format === VAL_SPEC) {\n      setCache(cacheKey, value);\n      return value;\n    }\n    // named-color\n  } else if (/^[a-z]+$/.test(value)) {\n    if (Object.prototype.hasOwnProperty.call(NAMED_COLORS, value)) {\n      if (format === VAL_SPEC) {\n        setCache(cacheKey, value);\n        return value;\n      }\n      [r, g, b] = NAMED_COLORS[\n        value as keyof typeof NAMED_COLORS\n      ] as TriColorChannels;\n      alpha = 1;\n    } else {\n      switch (format) {\n        case VAL_SPEC: {\n          if (value === 'transparent') {\n            setCache(cacheKey, value);\n            return value;\n          }\n          const res = '';\n          setCache(cacheKey, res);\n          return res;\n        }\n        case VAL_MIX: {\n          if (value === 'transparent') {\n            const res: SpecifiedColorChannels = ['rgb', 0, 0, 0, 0];\n            setCache(cacheKey, res);\n            return res;\n          }\n          setCache(cacheKey, null);\n          return new NullObject();\n        }\n        case VAL_COMP:\n        default: {\n          if (nullable && value !== 'transparent') {\n            setCache(cacheKey, null);\n            return new NullObject();\n          }\n          const res: SpecifiedColorChannels = ['rgb', 0, 0, 0, 0];\n          setCache(cacheKey, res);\n          return res;\n        }\n      }\n    }\n    // hex-color\n  } else if (value[0] === '#') {\n    [r, g, b, alpha] = convertHexToRgb(value);\n    // hsl()\n  } else if (value.startsWith('hsl')) {\n    [, r, g, b, alpha] = parseHsl(value, opt) as ComputedColorChannels;\n    // hwb()\n  } else if (value.startsWith('hwb')) {\n    [, r, g, b, alpha] = parseHwb(value, opt) as ComputedColorChannels;\n    // lab(), lch()\n  } else if (/^l(?:ab|ch)/.test(value)) {\n    let x, y, z;\n    if (value.startsWith('lab')) {\n      [cs, x, y, z, alpha] = parseLab(value, opt) as ComputedColorChannels;\n    } else {\n      [cs, x, y, z, alpha] = parseLch(value, opt) as ComputedColorChannels;\n    }\n    if (REG_SPEC.test(format)) {\n      const res: SpecifiedColorChannels = [cs, x, y, z, alpha];\n      setCache(cacheKey, res);\n      return res;\n    }\n    [r, g, b] = transformXyzD50ToRgb([x, y, z]);\n    // oklab(), oklch()\n  } else if (/^okl(?:ab|ch)/.test(value)) {\n    let x, y, z;\n    if (value.startsWith('oklab')) {\n      [cs, x, y, z, alpha] = parseOklab(value, opt) as ComputedColorChannels;\n    } else {\n      [cs, x, y, z, alpha] = parseOklch(value, opt) as ComputedColorChannels;\n    }\n    if (REG_SPEC.test(format)) {\n      const res: SpecifiedColorChannels = [cs, x, y, z, alpha];\n      setCache(cacheKey, res);\n      return res;\n    }\n    [r, g, b] = transformXyzToRgb([x, y, z]);\n    // rgb()\n  } else {\n    [, r, g, b, alpha] = parseRgb(value, opt) as ComputedColorChannels;\n  }\n  if (format === VAL_MIX && colorSpace === 'srgb') {\n    const res: SpecifiedColorChannels = [\n      'srgb',\n      r / MAX_RGB,\n      g / MAX_RGB,\n      b / MAX_RGB,\n      alpha\n    ];\n    setCache(cacheKey, res);\n    return res;\n  }\n  const res: SpecifiedColorChannels = [\n    'rgb',\n    Math.round(r),\n    Math.round(g),\n    Math.round(b),\n    alpha\n  ];\n  setCache(cacheKey, res);\n  return res;\n};\n\n/**\n * resolve color()\n * @param value - color function value\n * @param [opt] - options\n * @returns resolved color - [cs, v1, v2, v3, alpha], '(empty)', NullObject\n */\nexport const resolveColorFunc = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { colorSpace = '', format = '', nullable = false } = opt;\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'resolveColorFunc',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    const cachedItem = cachedResult.item;\n    if (isString(cachedItem)) {\n      return cachedItem as string;\n    }\n    return cachedItem as SpecifiedColorChannels;\n  }\n  if (!REG_FN_COLOR.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      setCache(cacheKey, null);\n      return res;\n    }\n    setCache(cacheKey, res);\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const [cs, v1, v2, v3, v4] = parseColorFunc(\n    value,\n    opt\n  ) as SpecifiedColorChannels;\n  if (REG_SPEC.test(format) || (format === VAL_MIX && cs === colorSpace)) {\n    const res: SpecifiedColorChannels = [cs, v1, v2, v3, v4];\n    setCache(cacheKey, res);\n    return res;\n  }\n  const x = parseFloat(`${v1}`);\n  const y = parseFloat(`${v2}`);\n  const z = parseFloat(`${v3}`);\n  const alpha = parseAlpha(`${v4}`);\n  const [r, g, b] = transformXyzToRgb([x, y, z], true);\n  const res: SpecifiedColorChannels = ['rgb', r, g, b, alpha];\n  setCache(cacheKey, res);\n  return res;\n};\n\n/**\n * convert color value to linear rgb\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject - [r, g, b, alpha] r|g|b|alpha: 0..1\n */\nexport const convertColorToLinearRgb = (\n  value: string,\n  opt: {\n    colorSpace?: string;\n    format?: string;\n  } = {}\n): ColorChannels | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { colorSpace = '', format = '' } = opt;\n  let cs = '';\n  let r, g, b, alpha, x, y, z;\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz instanceof NullObject) {\n      return xyz;\n    }\n    [cs, x, y, z, alpha] = xyz as ComputedColorChannels;\n    if (cs === colorSpace) {\n      return [x, y, z, alpha];\n    }\n    [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, [x, y, z], true);\n  } else if (value.startsWith(FN_COLOR)) {\n    const [, val] = value.match(REG_FN_COLOR) as MatchedRegExp;\n    const [cs] = val\n      .replace('/', ' ')\n      .split(/\\s+/) as StringColorSpacedChannels;\n    if (cs === 'srgb-linear') {\n      [, r, g, b, alpha] = resolveColorFunc(value, {\n        format: VAL_COMP\n      }) as ComputedColorChannels;\n    } else {\n      [, x, y, z, alpha] = parseColorFunc(value) as ComputedColorChannels;\n      [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, [x, y, z], true);\n    }\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value) as ComputedColorChannels;\n    [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, [x, y, z], true);\n  }\n  return [\n    Math.min(Math.max(r, 0), 1),\n    Math.min(Math.max(g, 0), 1),\n    Math.min(Math.max(b, 0), 1),\n    alpha\n  ];\n};\n\n/**\n * convert color value to rgb\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject\n *   - [r, g, b, alpha] r|g|b: 0..255 alpha: 0..1\n */\nexport const convertColorToRgb = (\n  value: string,\n  opt: Options = {}\n): ColorChannels | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '' } = opt;\n  let r, g, b, alpha;\n  if (format === VAL_MIX) {\n    let rgb;\n    if (value.startsWith(FN_COLOR)) {\n      rgb = resolveColorFunc(value, opt);\n    } else {\n      rgb = resolveColorValue(value, opt);\n    }\n    if (rgb instanceof NullObject) {\n      return rgb;\n    }\n    [, r, g, b, alpha] = rgb as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    const [, val] = value.match(REG_FN_COLOR) as MatchedRegExp;\n    const [cs] = val\n      .replace('/', ' ')\n      .split(/\\s+/) as StringColorSpacedChannels;\n    if (cs === 'srgb') {\n      [, r, g, b, alpha] = resolveColorFunc(value, {\n        format: VAL_COMP\n      }) as ComputedColorChannels;\n      r *= MAX_RGB;\n      g *= MAX_RGB;\n      b *= MAX_RGB;\n    } else {\n      [, r, g, b, alpha] = resolveColorFunc(value) as ComputedColorChannels;\n    }\n  } else if (/^(?:ok)?l(?:ab|ch)/.test(value)) {\n    [r, g, b, alpha] = convertColorToLinearRgb(value) as ColorChannels;\n    [r, g, b] = transformLinearRgbToRgb([r, g, b]);\n  } else {\n    [, r, g, b, alpha] = resolveColorValue(value, {\n      format: VAL_COMP\n    }) as ComputedColorChannels;\n  }\n  return [r, g, b, alpha];\n};\n\n/**\n * convert color value to xyz\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject - [x, y, z, alpha]\n */\nexport const convertColorToXyz = (\n  value: string,\n  opt: Options = {}\n): ColorChannels | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { d50 = false, format = '' } = opt;\n  let x, y, z, alpha;\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz instanceof NullObject) {\n      return xyz;\n    }\n    [, x, y, z, alpha] = xyz as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    const [, val] = value.match(REG_FN_COLOR) as MatchedRegExp;\n    const [cs] = val\n      .replace('/', ' ')\n      .split(/\\s+/) as StringColorSpacedChannels;\n    if (d50) {\n      if (cs === 'xyz-d50') {\n        [, x, y, z, alpha] = resolveColorFunc(value, {\n          format: VAL_COMP\n        }) as ComputedColorChannels;\n      } else {\n        [, x, y, z, alpha] = parseColorFunc(\n          value,\n          opt\n        ) as ComputedColorChannels;\n      }\n    } else if (/^xyz(?:-d65)?$/.test(cs)) {\n      [, x, y, z, alpha] = resolveColorFunc(value, {\n        format: VAL_COMP\n      }) as ComputedColorChannels;\n    } else {\n      [, x, y, z, alpha] = parseColorFunc(value) as ComputedColorChannels;\n    }\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value, opt) as ComputedColorChannels;\n  }\n  return [x, y, z, alpha];\n};\n\n/**\n * convert color value to hsl\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject - [h, s, l, alpha], hue may be powerless\n */\nexport const convertColorToHsl = (\n  value: string,\n  opt: Options = {}\n): ColorChannels | [number | string, number, number, number] | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '' } = opt;\n  let h, s, l, alpha;\n  if (REG_HSL.test(value)) {\n    [, h, s, l, alpha] = parseHsl(value, {\n      format: 'hsl'\n    }) as ComputedColorChannels;\n    if (format === 'hsl') {\n      return [Math.round(h), Math.round(s), Math.round(l), alpha];\n    }\n    return [h, s, l, alpha];\n  }\n  let x, y, z;\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz instanceof NullObject) {\n      return xyz;\n    }\n    [, x, y, z, alpha] = xyz as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value) as ComputedColorChannels;\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value) as ComputedColorChannels;\n  }\n  [h, s, l] = transformXyzToHsl([x, y, z], true) as TriColorChannels;\n  if (format === 'hsl') {\n    return [Math.round(h), Math.round(s), Math.round(l), alpha];\n  }\n  return [format === VAL_MIX && s === 0 ? NONE : h, s, l, alpha];\n};\n\n/**\n * convert color value to hwb\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject - [h, w, b, alpha], hue may be powerless\n */\nexport const convertColorToHwb = (\n  value: string,\n  opt: Options = {}\n): ColorChannels | [number | string, number, number, number] | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '' } = opt;\n  let h, w, b, alpha;\n  if (REG_HWB.test(value)) {\n    [, h, w, b, alpha] = parseHwb(value, {\n      format: 'hwb'\n    }) as ComputedColorChannels;\n    if (format === 'hwb') {\n      return [Math.round(h), Math.round(w), Math.round(b), alpha];\n    }\n    return [h, w, b, alpha];\n  }\n  let x, y, z;\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz instanceof NullObject) {\n      return xyz;\n    }\n    [, x, y, z, alpha] = xyz as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value) as ComputedColorChannels;\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value) as ComputedColorChannels;\n  }\n  [h, w, b] = transformXyzToHwb([x, y, z], true) as TriColorChannels;\n  if (format === 'hwb') {\n    return [Math.round(h), Math.round(w), Math.round(b), alpha];\n  }\n  return [format === VAL_MIX && w + b >= 100 ? NONE : h, w, b, alpha];\n};\n\n/**\n * convert color value to lab\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject - [l, a, b, alpha]\n */\nexport const convertColorToLab = (\n  value: string,\n  opt: Options = {}\n): ColorChannels | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '' } = opt;\n  let l, a, b, alpha;\n  if (REG_LAB.test(value)) {\n    [, l, a, b, alpha] = parseLab(value, {\n      format: VAL_COMP\n    }) as ComputedColorChannels;\n    return [l, a, b, alpha];\n  }\n  let x, y, z;\n  if (format === VAL_MIX) {\n    let xyz;\n    opt.d50 = true;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz instanceof NullObject) {\n      return xyz;\n    }\n    [, x, y, z, alpha] = xyz as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value, {\n      d50: true\n    }) as ComputedColorChannels;\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value, {\n      d50: true\n    }) as ComputedColorChannels;\n  }\n  [l, a, b] = transformXyzD50ToLab([x, y, z], true);\n  return [l, a, b, alpha];\n};\n\n/**\n * convert color value to lch\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject - [l, c, h, alpha], hue may be powerless\n */\nexport const convertColorToLch = (\n  value: string,\n  opt: Options = {}\n): ColorChannels | [number, number, number | string, number] | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '' } = opt;\n  let l, c, h, alpha;\n  if (REG_LCH.test(value)) {\n    [, l, c, h, alpha] = parseLch(value, {\n      format: VAL_COMP\n    }) as ComputedColorChannels;\n    return [l, c, h, alpha];\n  }\n  let x, y, z;\n  if (format === VAL_MIX) {\n    let xyz;\n    opt.d50 = true;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz instanceof NullObject) {\n      return xyz;\n    }\n    [, x, y, z, alpha] = xyz as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value, {\n      d50: true\n    }) as ComputedColorChannels;\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value, {\n      d50: true\n    }) as ComputedColorChannels;\n  }\n  [l, c, h] = transformXyzD50ToLch([x, y, z], true);\n  return [l, c, format === VAL_MIX && c === 0 ? NONE : h, alpha];\n};\n\n/**\n * convert color value to oklab\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject - [l, a, b, alpha]\n */\nexport const convertColorToOklab = (\n  value: string,\n  opt: Options = {}\n): ColorChannels | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '' } = opt;\n  let l, a, b, alpha;\n  if (REG_OKLAB.test(value)) {\n    [, l, a, b, alpha] = parseOklab(value, {\n      format: VAL_COMP\n    }) as ComputedColorChannels;\n    return [l, a, b, alpha];\n  }\n  let x, y, z;\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz instanceof NullObject) {\n      return xyz;\n    }\n    [, x, y, z, alpha] = xyz as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value) as ComputedColorChannels;\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value) as ComputedColorChannels;\n  }\n  [l, a, b] = transformXyzToOklab([x, y, z], true);\n  return [l, a, b, alpha];\n};\n\n/**\n * convert color value to oklch\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject - [l, c, h, alpha], hue may be powerless\n */\nexport const convertColorToOklch = (\n  value: string,\n  opt: Options = {}\n): ColorChannels | [number, number, number | string, number] | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '' } = opt;\n  let l, c, h, alpha;\n  if (REG_OKLCH.test(value)) {\n    [, l, c, h, alpha] = parseOklch(value, {\n      format: VAL_COMP\n    }) as ComputedColorChannels;\n    return [l, c, h, alpha];\n  }\n  let x, y, z;\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz instanceof NullObject) {\n      return xyz;\n    }\n    [, x, y, z, alpha] = xyz as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value) as ComputedColorChannels;\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value) as ComputedColorChannels;\n  }\n  [l, c, h] = transformXyzToOklch([x, y, z], true) as TriColorChannels;\n  return [l, c, format === VAL_MIX && c === 0 ? NONE : h, alpha];\n};\n\n/**\n * resolve color-mix()\n * @param value - color-mix color value\n * @param [opt] - options\n * @returns resolved color - [cs, v1, v2, v3, alpha], '(empty)'\n */\nexport const resolveColorMix = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '', nullable = false } = opt;\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'resolveColorMix',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    const cachedItem = cachedResult.item;\n    if (isString(cachedItem)) {\n      return cachedItem as string;\n    }\n    return cachedItem as SpecifiedColorChannels;\n  }\n  const nestedItems = [];\n  if (!REG_MIX.test(value)) {\n    if (value.startsWith(FN_MIX) && REG_MIX_NEST.test(value)) {\n      const regColorSpace = new RegExp(`^(?:${CS_RGB}|${CS_XYZ})$`);\n      const items = value.match(REG_MIX_NEST) as RegExpMatchArray;\n      for (const item of items) {\n        if (item) {\n          let val = resolveColorMix(item, {\n            format: format === VAL_SPEC ? format : VAL_COMP\n          }) as ComputedColorChannels | string;\n          // computed value\n          if (Array.isArray(val)) {\n            const [cs, v1, v2, v3, v4] = val as ComputedColorChannels;\n            if (v1 === 0 && v2 === 0 && v3 === 0 && v4 === 0) {\n              value = '';\n              break;\n            }\n            if (regColorSpace.test(cs)) {\n              if (v4 === 1) {\n                val = `color(${cs} ${v1} ${v2} ${v3})`;\n              } else {\n                val = `color(${cs} ${v1} ${v2} ${v3} / ${v4})`;\n              }\n            } else if (v4 === 1) {\n              val = `${cs}(${v1} ${v2} ${v3})`;\n            } else {\n              val = `${cs}(${v1} ${v2} ${v3} / ${v4})`;\n            }\n          } else if (!REG_MIX.test(val)) {\n            value = '';\n            break;\n          }\n          nestedItems.push(val);\n          value = value.replace(item, val);\n        }\n      }\n      if (!value) {\n        const res = cacheInvalidColorValue(cacheKey, format, nullable);\n        return res;\n      }\n    } else {\n      const res = cacheInvalidColorValue(cacheKey, format, nullable);\n      return res;\n    }\n  }\n  let colorSpace = '';\n  let hueArc = '';\n  let colorA = '';\n  let pctA = '';\n  let colorB = '';\n  let pctB = '';\n  if (nestedItems.length && format === VAL_SPEC) {\n    const regColorSpace = new RegExp(`^color-mix\\\\(\\\\s*in\\\\s+(${CS_MIX})\\\\s*,`);\n    const [, cs] = value.match(regColorSpace) as MatchedRegExp;\n    if (REG_CS_HUE.test(cs)) {\n      [, colorSpace, hueArc] = cs.match(REG_CS_HUE) as MatchedRegExp;\n    } else {\n      colorSpace = cs;\n    }\n    if (nestedItems.length === 2) {\n      let [itemA, itemB] = nestedItems as [string, string];\n      itemA = itemA.replace(/(?=[()])/g, '\\\\');\n      itemB = itemB.replace(/(?=[()])/g, '\\\\');\n      const regA = new RegExp(`(${itemA})(?:\\\\s+(${PCT}))?`);\n      const regB = new RegExp(`(${itemB})(?:\\\\s+(${PCT}))?`);\n      [, colorA, pctA] = value.match(regA) as MatchedRegExp;\n      [, colorB, pctB] = value.match(regB) as MatchedRegExp;\n    } else {\n      let [item] = nestedItems as [string];\n      item = item.replace(/(?=[()])/g, '\\\\');\n      const itemPart = `${item}(?:\\\\s+${PCT})?`;\n      const itemPartCapt = `(${item})(?:\\\\s+(${PCT}))?`;\n      const regItemPart = new RegExp(`^${itemPartCapt}$`);\n      const regLastItem = new RegExp(`${itemPartCapt}\\\\s*\\\\)$`);\n      const regColorPart = new RegExp(`^(${SYN_COLOR_TYPE})(?:\\\\s+(${PCT}))?$`);\n      // item is at the end\n      if (regLastItem.test(value)) {\n        const reg = new RegExp(\n          `(${SYN_MIX_PART})\\\\s*,\\\\s*(${itemPart})\\\\s*\\\\)$`\n        );\n        const [, colorPartA, colorPartB] = value.match(reg) as MatchedRegExp;\n        [, colorA, pctA] = colorPartA.match(regColorPart) as MatchedRegExp;\n        [, colorB, pctB] = colorPartB.match(regItemPart) as MatchedRegExp;\n      } else {\n        const reg = new RegExp(\n          `(${itemPart})\\\\s*,\\\\s*(${SYN_MIX_PART})\\\\s*\\\\)$`\n        );\n        const [, colorPartA, colorPartB] = value.match(reg) as MatchedRegExp;\n        [, colorA, pctA] = colorPartA.match(regItemPart) as MatchedRegExp;\n        [, colorB, pctB] = colorPartB.match(regColorPart) as MatchedRegExp;\n      }\n    }\n  } else {\n    const [, cs, colorPartA, colorPartB] = value.match(\n      REG_MIX_CAPT\n    ) as MatchedRegExp;\n    const reg = new RegExp(`^(${SYN_COLOR_TYPE})(?:\\\\s+(${PCT}))?$`);\n    [, colorA, pctA] = colorPartA.match(reg) as MatchedRegExp;\n    [, colorB, pctB] = colorPartB.match(reg) as MatchedRegExp;\n    if (REG_CS_HUE.test(cs)) {\n      [, colorSpace, hueArc] = cs.match(REG_CS_HUE) as MatchedRegExp;\n    } else {\n      colorSpace = cs;\n    }\n  }\n  // normalize percentages and set multipler\n  let pA, pB, m;\n  if (pctA && pctB) {\n    const p1 = parseFloat(pctA) / MAX_PCT;\n    const p2 = parseFloat(pctB) / MAX_PCT;\n    if (p1 < 0 || p1 > 1 || p2 < 0 || p2 > 1) {\n      const res = cacheInvalidColorValue(cacheKey, format, nullable);\n      return res;\n    }\n    const factor = p1 + p2;\n    if (factor === 0) {\n      const res = cacheInvalidColorValue(cacheKey, format, nullable);\n      return res;\n    }\n    pA = p1 / factor;\n    pB = p2 / factor;\n    m = factor < 1 ? factor : 1;\n  } else {\n    if (pctA) {\n      pA = parseFloat(pctA) / MAX_PCT;\n      if (pA < 0 || pA > 1) {\n        const res = cacheInvalidColorValue(cacheKey, format, nullable);\n        return res;\n      }\n      pB = 1 - pA;\n    } else if (pctB) {\n      pB = parseFloat(pctB) / MAX_PCT;\n      if (pB < 0 || pB > 1) {\n        const res = cacheInvalidColorValue(cacheKey, format, nullable);\n        return res;\n      }\n      pA = 1 - pB;\n    } else {\n      pA = HALF;\n      pB = HALF;\n    }\n    m = 1;\n  }\n  if (colorSpace === 'xyz') {\n    colorSpace = 'xyz-d65';\n  }\n  // specified value\n  if (format === VAL_SPEC) {\n    let valueA = '';\n    let valueB = '';\n    if (colorA.startsWith(FN_MIX)) {\n      valueA = colorA;\n    } else if (colorA.startsWith(FN_COLOR)) {\n      const [cs, v1, v2, v3, v4] = parseColorFunc(\n        colorA,\n        opt\n      ) as SpecifiedColorChannels;\n      if (v4 === 1) {\n        valueA = `color(${cs} ${v1} ${v2} ${v3})`;\n      } else {\n        valueA = `color(${cs} ${v1} ${v2} ${v3} / ${v4})`;\n      }\n    } else {\n      const val = parseColorValue(colorA, opt);\n      if (Array.isArray(val)) {\n        const [cs, v1, v2, v3, v4] = val;\n        if (v4 === 1) {\n          if (cs === 'rgb') {\n            valueA = `${cs}(${v1}, ${v2}, ${v3})`;\n          } else {\n            valueA = `${cs}(${v1} ${v2} ${v3})`;\n          }\n        } else if (cs === 'rgb') {\n          valueA = `${cs}a(${v1}, ${v2}, ${v3}, ${v4})`;\n        } else {\n          valueA = `${cs}(${v1} ${v2} ${v3} / ${v4})`;\n        }\n      } else {\n        if (!isString(val) || !val) {\n          setCache(cacheKey, '');\n          return '';\n        }\n        valueA = val;\n      }\n    }\n    if (colorB.startsWith(FN_MIX)) {\n      valueB = colorB;\n    } else if (colorB.startsWith(FN_COLOR)) {\n      const [cs, v1, v2, v3, v4] = parseColorFunc(\n        colorB,\n        opt\n      ) as SpecifiedColorChannels;\n      if (v4 === 1) {\n        valueB = `color(${cs} ${v1} ${v2} ${v3})`;\n      } else {\n        valueB = `color(${cs} ${v1} ${v2} ${v3} / ${v4})`;\n      }\n    } else {\n      const val = parseColorValue(colorB, opt);\n      if (Array.isArray(val)) {\n        const [cs, v1, v2, v3, v4] = val;\n        if (v4 === 1) {\n          if (cs === 'rgb') {\n            valueB = `${cs}(${v1}, ${v2}, ${v3})`;\n          } else {\n            valueB = `${cs}(${v1} ${v2} ${v3})`;\n          }\n        } else if (cs === 'rgb') {\n          valueB = `${cs}a(${v1}, ${v2}, ${v3}, ${v4})`;\n        } else {\n          valueB = `${cs}(${v1} ${v2} ${v3} / ${v4})`;\n        }\n      } else {\n        if (!isString(val) || !val) {\n          setCache(cacheKey, '');\n          return '';\n        }\n        valueB = val;\n      }\n    }\n    if (pctA && pctB) {\n      valueA += ` ${parseFloat(pctA)}%`;\n      valueB += ` ${parseFloat(pctB)}%`;\n    } else if (pctA) {\n      const pA = parseFloat(pctA);\n      if (pA !== MAX_PCT * HALF) {\n        valueA += ` ${pA}%`;\n      }\n    } else if (pctB) {\n      const pA = MAX_PCT - parseFloat(pctB);\n      if (pA !== MAX_PCT * HALF) {\n        valueA += ` ${pA}%`;\n      }\n    }\n    if (hueArc) {\n      const res = `color-mix(in ${colorSpace} ${hueArc} hue, ${valueA}, ${valueB})`;\n      setCache(cacheKey, res);\n      return res;\n    } else {\n      const res = `color-mix(in ${colorSpace}, ${valueA}, ${valueB})`;\n      setCache(cacheKey, res);\n      return res;\n    }\n  }\n  let r = 0;\n  let g = 0;\n  let b = 0;\n  let alpha = 0;\n  // in srgb, srgb-linear\n  if (/^srgb(?:-linear)?$/.test(colorSpace)) {\n    let rgbA, rgbB;\n    if (colorSpace === 'srgb') {\n      if (REG_CURRENT.test(colorA)) {\n        rgbA = [NONE, NONE, NONE, NONE];\n      } else {\n        rgbA = convertColorToRgb(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB)) {\n        rgbB = [NONE, NONE, NONE, NONE];\n      } else {\n        rgbB = convertColorToRgb(colorB, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    } else {\n      if (REG_CURRENT.test(colorA)) {\n        rgbA = [NONE, NONE, NONE, NONE];\n      } else {\n        rgbA = convertColorToLinearRgb(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB)) {\n        rgbB = [NONE, NONE, NONE, NONE];\n      } else {\n        rgbB = convertColorToLinearRgb(colorB, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    }\n    if (rgbA instanceof NullObject || rgbB instanceof NullObject) {\n      const res = cacheInvalidColorValue(cacheKey, format, nullable);\n      return res;\n    }\n    const [rrA, ggA, bbA, aaA] = rgbA as NumStrColorChannels;\n    const [rrB, ggB, bbB, aaB] = rgbB as NumStrColorChannels;\n    const rNone = rrA === NONE && rrB === NONE;\n    const gNone = ggA === NONE && ggB === NONE;\n    const bNone = bbA === NONE && bbB === NONE;\n    const alphaNone = aaA === NONE && aaB === NONE;\n    const [[rA, gA, bA, alphaA], [rB, gB, bB, alphaB]] =\n      normalizeColorComponents(\n        [rrA, ggA, bbA, aaA],\n        [rrB, ggB, bbB, aaB],\n        true\n      );\n    const factorA = alphaA * pA;\n    const factorB = alphaB * pB;\n    alpha = factorA + factorB;\n    if (alpha === 0) {\n      r = rA * pA + rB * pB;\n      g = gA * pA + gB * pB;\n      b = bA * pA + bB * pB;\n    } else {\n      r = (rA * factorA + rB * factorB) / alpha;\n      g = (gA * factorA + gB * factorB) / alpha;\n      b = (bA * factorA + bB * factorB) / alpha;\n      alpha = parseFloat(alpha.toFixed(3));\n    }\n    if (format === VAL_COMP) {\n      const res: SpecifiedColorChannels = [\n        colorSpace,\n        rNone ? NONE : roundToPrecision(r, HEX),\n        gNone ? NONE : roundToPrecision(g, HEX),\n        bNone ? NONE : roundToPrecision(b, HEX),\n        alphaNone ? NONE : alpha * m\n      ];\n      setCache(cacheKey, res);\n      return res;\n    }\n    r *= MAX_RGB;\n    g *= MAX_RGB;\n    b *= MAX_RGB;\n    // in xyz, xyz-d65, xyz-d50\n  } else if (REG_CS_XYZ.test(colorSpace)) {\n    let xyzA, xyzB;\n    if (REG_CURRENT.test(colorA)) {\n      xyzA = [NONE, NONE, NONE, NONE];\n    } else {\n      xyzA = convertColorToXyz(colorA, {\n        colorSpace,\n        d50: colorSpace === 'xyz-d50',\n        format: VAL_MIX\n      });\n    }\n    if (REG_CURRENT.test(colorB)) {\n      xyzB = [NONE, NONE, NONE, NONE];\n    } else {\n      xyzB = convertColorToXyz(colorB, {\n        colorSpace,\n        d50: colorSpace === 'xyz-d50',\n        format: VAL_MIX\n      });\n    }\n    if (xyzA instanceof NullObject || xyzB instanceof NullObject) {\n      const res = cacheInvalidColorValue(cacheKey, format, nullable);\n      return res;\n    }\n    const [xxA, yyA, zzA, aaA] = xyzA;\n    const [xxB, yyB, zzB, aaB] = xyzB;\n    const xNone = xxA === NONE && xxB === NONE;\n    const yNone = yyA === NONE && yyB === NONE;\n    const zNone = zzA === NONE && zzB === NONE;\n    const alphaNone = aaA === NONE && aaB === NONE;\n    const [[xA, yA, zA, alphaA], [xB, yB, zB, alphaB]] =\n      normalizeColorComponents(\n        [xxA, yyA, zzA, aaA],\n        [xxB, yyB, zzB, aaB],\n        true\n      );\n    const factorA = alphaA * pA;\n    const factorB = alphaB * pB;\n    alpha = factorA + factorB;\n    let x, y, z;\n    if (alpha === 0) {\n      x = xA * pA + xB * pB;\n      y = yA * pA + yB * pB;\n      z = zA * pA + zB * pB;\n    } else {\n      x = (xA * factorA + xB * factorB) / alpha;\n      y = (yA * factorA + yB * factorB) / alpha;\n      z = (zA * factorA + zB * factorB) / alpha;\n      alpha = parseFloat(alpha.toFixed(3));\n    }\n    if (format === VAL_COMP) {\n      const res: SpecifiedColorChannels = [\n        colorSpace,\n        xNone ? NONE : roundToPrecision(x, HEX),\n        yNone ? NONE : roundToPrecision(y, HEX),\n        zNone ? NONE : roundToPrecision(z, HEX),\n        alphaNone ? NONE : alpha * m\n      ];\n      setCache(cacheKey, res);\n      return res;\n    }\n    if (colorSpace === 'xyz-d50') {\n      [r, g, b] = transformXyzD50ToRgb([x, y, z], true);\n    } else {\n      [r, g, b] = transformXyzToRgb([x, y, z], true);\n    }\n    // in hsl, hwb\n  } else if (/^h(?:sl|wb)$/.test(colorSpace)) {\n    let hslA, hslB;\n    if (colorSpace === 'hsl') {\n      if (REG_CURRENT.test(colorA)) {\n        hslA = [NONE, NONE, NONE, NONE];\n      } else {\n        hslA = convertColorToHsl(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB)) {\n        hslB = [NONE, NONE, NONE, NONE];\n      } else {\n        hslB = convertColorToHsl(colorB, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    } else {\n      if (REG_CURRENT.test(colorA)) {\n        hslA = [NONE, NONE, NONE, NONE];\n      } else {\n        hslA = convertColorToHwb(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB)) {\n        hslB = [NONE, NONE, NONE, NONE];\n      } else {\n        hslB = convertColorToHwb(colorB, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    }\n    if (hslA instanceof NullObject || hslB instanceof NullObject) {\n      const res = cacheInvalidColorValue(cacheKey, format, nullable);\n      return res;\n    }\n    const [hhA, ssA, llA, aaA] = hslA;\n    const [hhB, ssB, llB, aaB] = hslB;\n    const alphaNone = aaA === NONE && aaB === NONE;\n    let [[hA, sA, lA, alphaA], [hB, sB, lB, alphaB]] = normalizeColorComponents(\n      [hhA, ssA, llA, aaA],\n      [hhB, ssB, llB, aaB],\n      true\n    );\n    if (hueArc) {\n      [hA, hB] = interpolateHue(hA, hB, hueArc);\n    }\n    const factorA = alphaA * pA;\n    const factorB = alphaB * pB;\n    alpha = factorA + factorB;\n    const h = (hA * pA + hB * pB) % DEG;\n    let s, l;\n    if (alpha === 0) {\n      s = sA * pA + sB * pB;\n      l = lA * pA + lB * pB;\n    } else {\n      s = (sA * factorA + sB * factorB) / alpha;\n      l = (lA * factorA + lB * factorB) / alpha;\n      alpha = parseFloat(alpha.toFixed(3));\n    }\n    [r, g, b] = convertColorToRgb(\n      `${colorSpace}(${h} ${s} ${l})`\n    ) as ColorChannels;\n    if (format === VAL_COMP) {\n      const res: SpecifiedColorChannels = [\n        'srgb',\n        roundToPrecision(r / MAX_RGB, HEX),\n        roundToPrecision(g / MAX_RGB, HEX),\n        roundToPrecision(b / MAX_RGB, HEX),\n        alphaNone ? NONE : alpha * m\n      ];\n      setCache(cacheKey, res);\n      return res;\n    }\n    // in lch, oklch\n  } else if (/^(?:ok)?lch$/.test(colorSpace)) {\n    let lchA, lchB;\n    if (colorSpace === 'lch') {\n      if (REG_CURRENT.test(colorA)) {\n        lchA = [NONE, NONE, NONE, NONE];\n      } else {\n        lchA = convertColorToLch(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB)) {\n        lchB = [NONE, NONE, NONE, NONE];\n      } else {\n        lchB = convertColorToLch(colorB, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    } else {\n      if (REG_CURRENT.test(colorA)) {\n        lchA = [NONE, NONE, NONE, NONE];\n      } else {\n        lchA = convertColorToOklch(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB)) {\n        lchB = [NONE, NONE, NONE, NONE];\n      } else {\n        lchB = convertColorToOklch(colorB, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    }\n    if (lchA instanceof NullObject || lchB instanceof NullObject) {\n      const res = cacheInvalidColorValue(cacheKey, format, nullable);\n      return res;\n    }\n    const [llA, ccA, hhA, aaA] = lchA;\n    const [llB, ccB, hhB, aaB] = lchB;\n    const lNone = llA === NONE && llB === NONE;\n    const cNone = ccA === NONE && ccB === NONE;\n    const hNone = hhA === NONE && hhB === NONE;\n    const alphaNone = aaA === NONE && aaB === NONE;\n    let [[lA, cA, hA, alphaA], [lB, cB, hB, alphaB]] = normalizeColorComponents(\n      [llA, ccA, hhA, aaA],\n      [llB, ccB, hhB, aaB],\n      true\n    );\n    if (hueArc) {\n      [hA, hB] = interpolateHue(hA, hB, hueArc);\n    }\n    const factorA = alphaA * pA;\n    const factorB = alphaB * pB;\n    alpha = factorA + factorB;\n    const h = (hA * pA + hB * pB) % DEG;\n    let l, c;\n    if (alpha === 0) {\n      l = lA * pA + lB * pB;\n      c = cA * pA + cB * pB;\n    } else {\n      l = (lA * factorA + lB * factorB) / alpha;\n      c = (cA * factorA + cB * factorB) / alpha;\n      alpha = parseFloat(alpha.toFixed(3));\n    }\n    if (format === VAL_COMP) {\n      const res: SpecifiedColorChannels = [\n        colorSpace,\n        lNone ? NONE : roundToPrecision(l, HEX),\n        cNone ? NONE : roundToPrecision(c, HEX),\n        hNone ? NONE : roundToPrecision(h, HEX),\n        alphaNone ? NONE : alpha * m\n      ];\n      setCache(cacheKey, res);\n      return res;\n    }\n    [, r, g, b] = resolveColorValue(\n      `${colorSpace}(${l} ${c} ${h})`\n    ) as ComputedColorChannels;\n    // in lab, oklab\n  } else {\n    let labA, labB;\n    if (colorSpace === 'lab') {\n      if (REG_CURRENT.test(colorA)) {\n        labA = [NONE, NONE, NONE, NONE];\n      } else {\n        labA = convertColorToLab(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB)) {\n        labB = [NONE, NONE, NONE, NONE];\n      } else {\n        labB = convertColorToLab(colorB, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    } else {\n      if (REG_CURRENT.test(colorA)) {\n        labA = [NONE, NONE, NONE, NONE];\n      } else {\n        labA = convertColorToOklab(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB)) {\n        labB = [NONE, NONE, NONE, NONE];\n      } else {\n        labB = convertColorToOklab(colorB, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    }\n    if (labA instanceof NullObject || labB instanceof NullObject) {\n      const res = cacheInvalidColorValue(cacheKey, format, nullable);\n      return res;\n    }\n    const [llA, aaA, bbA, alA] = labA;\n    const [llB, aaB, bbB, alB] = labB;\n    const lNone = llA === NONE && llB === NONE;\n    const aNone = aaA === NONE && aaB === NONE;\n    const bNone = bbA === NONE && bbB === NONE;\n    const alphaNone = alA === NONE && alB === NONE;\n    const [[lA, aA, bA, alphaA], [lB, aB, bB, alphaB]] =\n      normalizeColorComponents(\n        [llA, aaA, bbA, alA],\n        [llB, aaB, bbB, alB],\n        true\n      );\n    const factorA = alphaA * pA;\n    const factorB = alphaB * pB;\n    alpha = factorA + factorB;\n    let l, aO, bO;\n    if (alpha === 0) {\n      l = lA * pA + lB * pB;\n      aO = aA * pA + aB * pB;\n      bO = bA * pA + bB * pB;\n    } else {\n      l = (lA * factorA + lB * factorB) / alpha;\n      aO = (aA * factorA + aB * factorB) / alpha;\n      bO = (bA * factorA + bB * factorB) / alpha;\n      alpha = parseFloat(alpha.toFixed(3));\n    }\n    if (format === VAL_COMP) {\n      const res: SpecifiedColorChannels = [\n        colorSpace,\n        lNone ? NONE : roundToPrecision(l, HEX),\n        aNone ? NONE : roundToPrecision(aO, HEX),\n        bNone ? NONE : roundToPrecision(bO, HEX),\n        alphaNone ? NONE : alpha * m\n      ];\n      setCache(cacheKey, res);\n      return res;\n    }\n    [, r, g, b] = resolveColorValue(\n      `${colorSpace}(${l} ${aO} ${bO})`\n    ) as ComputedColorChannels;\n  }\n  const res: SpecifiedColorChannels = [\n    'rgb',\n    Math.round(r),\n    Math.round(g),\n    Math.round(b),\n    parseFloat((alpha * m).toFixed(3))\n  ];\n  setCache(cacheKey, res);\n  return res;\n};\n"], "names": ["res", "val", "cs", "pA"], "mappings": ";;;;AAqDA,MAAM,YAAY;AAGlB,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,WAAW;AACjB,MAAM,MAAM;AACZ,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,MAAM,WAAW;AACjB,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM,gBAAgB;AACtB,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,cAAc,MAAM;AAC1B,MAAM,YAAY,QAAQ;AA4B1B,MAAM,MAAwB;AAAA,EAC5B,SAAS;AAAA,EACT;AAAA,GACC,IAAM,SAAS,UAAU;AAC5B;AACA,MAAM,oBAAiC;AAAA,EACrC,CAAC,mBAAmB,sBAAsB,mBAAmB;AAAA,EAC7D,CAAC,qBAAqB,oBAAoB,oBAAoB;AAAA,EAC9D,CAAC,sBAAsB,uBAAuB,iBAAiB;AACjE;AACA,MAAM,oBAAiC;AAAA,EACrC,CAAC,oBAAoB,sBAAsB,oBAAoB;AAAA,EAC/D,CAAC,qBAAqB,oBAAoB,qBAAqB;AAAA,EAC/D,CAAC,uBAAuB,sBAAsB,kBAAkB;AAClE;AAGA,MAAM,sBAAmC;AAAA,EACvC,CAAC,SAAS,SAAS,QAAQ,QAAQ,QAAQ,KAAK;AAAA,EAChD,CAAC,QAAQ,QAAQ,SAAS,QAAQ,QAAQ,MAAM;AAAA,EAChD,CAAC,OAAO,QAAQ,QAAQ,QAAQ,UAAU,OAAO;AACnD;AACA,MAAM,sBAAmC;AAAA,EACvC,CAAC,QAAQ,MAAM,OAAO,KAAK,QAAQ,IAAI;AAAA,EACvC,CAAC,UAAU,QAAQ,UAAU,QAAQ,QAAQ,MAAM;AAAA,EACnD,CAAC,MAAM,OAAO,QAAQ,OAAO,MAAM,GAAG;AACxC;AACA,MAAM,oBAAiC;AAAA,EACrC,CAAC,mBAAmB,oBAAoB,mBAAmB;AAAA,EAC3D,CAAC,oBAAoB,oBAAoB,kBAAkB;AAAA,EAC3D,CAAC,oBAAoB,oBAAoB,kBAAkB;AAC7D;AACA,MAAM,oBAAiC;AAAA,EACrC,CAAC,oBAAoB,qBAAqB,kBAAkB;AAAA,EAC5D,CAAC,qBAAqB,mBAAmB,mBAAmB;AAAA,EAC5D,CAAC,qBAAqB,qBAAqB,kBAAkB;AAC/D;AACA,MAAM,sBAAmC;AAAA,EACvC,CAAC,GAAK,oBAAoB,kBAAkB;AAAA,EAC5C,CAAC,GAAK,qBAAqB,mBAAmB;AAAA,EAC9C,CAAC,GAAK,qBAAqB,mBAAmB;AAChD;AACA,MAAM,sBAAmC;AAAA,EACvC,CAAC,mBAAmB,oBAAoB,mBAAmB;AAAA,EAC3D,CAAC,oBAAoB,mBAAqB,iBAAiB;AAAA,EAC3D,CAAC,oBAAoB,oBAAoB,mBAAmB;AAC9D;AACA,MAAM,mBAAgC;AAAA,EACpC,CAAC,SAAS,SAAS,SAAS,QAAQ,SAAS,OAAO;AAAA,EACpD,CAAC,QAAQ,QAAQ,SAAS,QAAQ,SAAS,OAAO;AAAA,EAClD,CAAC,IAAI,GAAG,QAAQ,QAAQ,UAAU,OAAO;AAC3C;AACA,MAAM,wBAAqC;AAAA,EACzC,CAAC,WAAW,UAAU,WAAW,WAAW,WAAW,SAAS;AAAA,EAChE,CAAC,WAAW,UAAU,YAAY,WAAW,UAAU,SAAS;AAAA,EAChE,CAAC,IAAI,GAAG,WAAW,WAAW,YAAY,SAAS;AACrD;AACA,MAAM,oBAAiC;AAAA,EACrC,CAAC,SAAS,QAAQ,SAAS,SAAS,SAAS,MAAM;AAAA,EACnD,CAAC,SAAS,SAAS,UAAU,SAAS,SAAS,OAAO;AAAA,EACtD,CAAC,QAAQ,SAAS,SAAS,SAAS,UAAU,OAAO;AACvD;AACA,MAAM,6BAA0C;AAAA,EAC9C,CAAC,oBAAoB,qBAAqB,kBAAkB;AAAA,EAC5D,CAAC,oBAAoB,mBAAmB,iBAAmB;AAAA,EAC3D,CAAC,GAAK,GAAK,kBAAkB;AAC/B;AAGA,MAAM,YAAY,IAAI,OAAO,OAAO,cAAc,IAAI;AACtD,MAAM,aAAa,IAAI,OAAO,IAAI,WAAW,GAAG;AAChD,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM,eAAe,IAAI,OAAO,iBAAiB,YAAY,WAAW;AACxE,MAAM,UAAU,IAAI,OAAO,iBAAiB,OAAO,IAAI,WAAW,WAAW;AAC7E,MAAM,UAAU,IAAI,OAAO,eAAe,OAAO,WAAW;AAC5D,MAAM,UAAU,IAAI,OAAO,eAAe,OAAO,WAAW;AAC5D,MAAM,UAAU,IAAI,OAAO,eAAe,OAAO,WAAW;AAC5D,MAAM,UAAU,IAAI,OAAO,IAAI,OAAO,GAAG;AACzC,MAAM,eAAe,IAAI,OAAO,IAAI,YAAY,GAAG;AACnD,MAAM,eAAe,IAAI,OAAO,GAAG,OAAO,IAAI,GAAG;AACjD,MAAM,YAAY,IAAI,OAAO,iBAAiB,OAAO,WAAW;AAChE,MAAM,YAAY,IAAI,OAAO,iBAAiB,OAAO,WAAW;AAChE,MAAM,WAAW;AAKV,MAAM,eAAe;AAAA,EAC1B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,cAAc,CAAC,KAAM,KAAM,GAAI;AAAA,EAC/B,MAAM,CAAC,GAAM,KAAM,GAAI;AAAA,EACvB,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,QAAQ,CAAC,KAAM,KAAM,GAAI;AAAA,EACzB,OAAO,CAAC,GAAM,GAAM,CAAI;AAAA,EACxB,gBAAgB,CAAC,KAAM,KAAM,GAAI;AAAA,EACjC,MAAM,CAAC,GAAM,GAAM,GAAI;AAAA,EACvB,YAAY,CAAC,KAAM,IAAM,GAAI;AAAA,EAC7B,OAAO,CAAC,KAAM,IAAM,EAAI;AAAA,EACxB,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,IAAM,KAAM,GAAI;AAAA,EAC5B,YAAY,CAAC,KAAM,KAAM,CAAI;AAAA,EAC7B,WAAW,CAAC,KAAM,KAAM,EAAI;AAAA,EAC5B,OAAO,CAAC,KAAM,KAAM,EAAI;AAAA,EACxB,gBAAgB,CAAC,KAAM,KAAM,GAAI;AAAA,EACjC,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,SAAS,CAAC,KAAM,IAAM,EAAI;AAAA,EAC1B,MAAM,CAAC,GAAM,KAAM,GAAI;AAAA,EACvB,UAAU,CAAC,GAAM,GAAM,GAAI;AAAA,EAC3B,UAAU,CAAC,GAAM,KAAM,GAAI;AAAA,EAC3B,eAAe,CAAC,KAAM,KAAM,EAAI;AAAA,EAChC,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,WAAW,CAAC,GAAM,KAAM,CAAI;AAAA,EAC5B,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,aAAa,CAAC,KAAM,GAAM,GAAI;AAAA,EAC9B,gBAAgB,CAAC,IAAM,KAAM,EAAI;AAAA,EACjC,YAAY,CAAC,KAAM,KAAM,CAAI;AAAA,EAC7B,YAAY,CAAC,KAAM,IAAM,GAAI;AAAA,EAC7B,SAAS,CAAC,KAAM,GAAM,CAAI;AAAA,EAC1B,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,cAAc,CAAC,KAAM,KAAM,GAAI;AAAA,EAC/B,eAAe,CAAC,IAAM,IAAM,GAAI;AAAA,EAChC,eAAe,CAAC,IAAM,IAAM,EAAI;AAAA,EAChC,eAAe,CAAC,IAAM,IAAM,EAAI;AAAA,EAChC,eAAe,CAAC,GAAM,KAAM,GAAI;AAAA,EAChC,YAAY,CAAC,KAAM,GAAM,GAAI;AAAA,EAC7B,UAAU,CAAC,KAAM,IAAM,GAAI;AAAA,EAC3B,aAAa,CAAC,GAAM,KAAM,GAAI;AAAA,EAC9B,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,YAAY,CAAC,IAAM,KAAM,GAAI;AAAA,EAC7B,WAAW,CAAC,KAAM,IAAM,EAAI;AAAA,EAC5B,aAAa,CAAC,KAAM,KAAM,GAAI;AAAA,EAC9B,aAAa,CAAC,IAAM,KAAM,EAAI;AAAA,EAC9B,SAAS,CAAC,KAAM,GAAM,GAAI;AAAA,EAC1B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,MAAM,CAAC,KAAM,KAAM,CAAI;AAAA,EACvB,WAAW,CAAC,KAAM,KAAM,EAAI;AAAA,EAC5B,MAAM,CAAC,KAAM,KAAM,GAAI;AAAA,EACvB,OAAO,CAAC,GAAM,KAAM,CAAI;AAAA,EACxB,aAAa,CAAC,KAAM,KAAM,EAAI;AAAA,EAC9B,MAAM,CAAC,KAAM,KAAM,GAAI;AAAA,EACvB,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,WAAW,CAAC,KAAM,IAAM,EAAI;AAAA,EAC5B,QAAQ,CAAC,IAAM,GAAM,GAAI;AAAA,EACzB,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,eAAe,CAAC,KAAM,KAAM,GAAI;AAAA,EAChC,WAAW,CAAC,KAAM,KAAM,CAAI;AAAA,EAC5B,cAAc,CAAC,KAAM,KAAM,GAAI;AAAA,EAC/B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,sBAAsB,CAAC,KAAM,KAAM,GAAI;AAAA,EACvC,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,aAAa,CAAC,KAAM,KAAM,GAAI;AAAA,EAC9B,eAAe,CAAC,IAAM,KAAM,GAAI;AAAA,EAChC,cAAc,CAAC,KAAM,KAAM,GAAI;AAAA,EAC/B,gBAAgB,CAAC,KAAM,KAAM,GAAI;AAAA,EACjC,gBAAgB,CAAC,KAAM,KAAM,GAAI;AAAA,EACjC,gBAAgB,CAAC,KAAM,KAAM,GAAI;AAAA,EACjC,aAAa,CAAC,KAAM,KAAM,GAAI;AAAA,EAC9B,MAAM,CAAC,GAAM,KAAM,CAAI;AAAA,EACvB,WAAW,CAAC,IAAM,KAAM,EAAI;AAAA,EAC5B,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,SAAS,CAAC,KAAM,GAAM,GAAI;AAAA,EAC1B,QAAQ,CAAC,KAAM,GAAM,CAAI;AAAA,EACzB,kBAAkB,CAAC,KAAM,KAAM,GAAI;AAAA,EACnC,YAAY,CAAC,GAAM,GAAM,GAAI;AAAA,EAC7B,cAAc,CAAC,KAAM,IAAM,GAAI;AAAA,EAC/B,cAAc,CAAC,KAAM,KAAM,GAAI;AAAA,EAC/B,gBAAgB,CAAC,IAAM,KAAM,GAAI;AAAA,EACjC,iBAAiB,CAAC,KAAM,KAAM,GAAI;AAAA,EAClC,mBAAmB,CAAC,GAAM,KAAM,GAAI;AAAA,EACpC,iBAAiB,CAAC,IAAM,KAAM,GAAI;AAAA,EAClC,iBAAiB,CAAC,KAAM,IAAM,GAAI;AAAA,EAClC,cAAc,CAAC,IAAM,IAAM,GAAI;AAAA,EAC/B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,aAAa,CAAC,KAAM,KAAM,GAAI;AAAA,EAC9B,MAAM,CAAC,GAAM,GAAM,GAAI;AAAA,EACvB,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,OAAO,CAAC,KAAM,KAAM,CAAI;AAAA,EACxB,WAAW,CAAC,KAAM,KAAM,EAAI;AAAA,EAC5B,QAAQ,CAAC,KAAM,KAAM,CAAI;AAAA,EACzB,WAAW,CAAC,KAAM,IAAM,CAAI;AAAA,EAC5B,QAAQ,CAAC,KAAM,KAAM,GAAI;AAAA,EACzB,eAAe,CAAC,KAAM,KAAM,GAAI;AAAA,EAChC,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,eAAe,CAAC,KAAM,KAAM,GAAI;AAAA,EAChC,eAAe,CAAC,KAAM,KAAM,GAAI;AAAA,EAChC,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,MAAM,CAAC,KAAM,KAAM,EAAI;AAAA,EACvB,MAAM,CAAC,KAAM,KAAM,GAAI;AAAA,EACvB,MAAM,CAAC,KAAM,KAAM,GAAI;AAAA,EACvB,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,QAAQ,CAAC,KAAM,GAAM,GAAI;AAAA,EACzB,eAAe,CAAC,KAAM,IAAM,GAAI;AAAA,EAChC,KAAK,CAAC,KAAM,GAAM,CAAI;AAAA,EACtB,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,IAAM,KAAM,GAAI;AAAA,EAC5B,aAAa,CAAC,KAAM,IAAM,EAAI;AAAA,EAC9B,QAAQ,CAAC,KAAM,KAAM,GAAI;AAAA,EACzB,YAAY,CAAC,KAAM,KAAM,EAAI;AAAA,EAC7B,UAAU,CAAC,IAAM,KAAM,EAAI;AAAA,EAC3B,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,QAAQ,CAAC,KAAM,IAAM,EAAI;AAAA,EACzB,QAAQ,CAAC,KAAM,KAAM,GAAI;AAAA,EACzB,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,WAAW,CAAC,KAAM,IAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,MAAM,CAAC,KAAM,KAAM,GAAI;AAAA,EACvB,aAAa,CAAC,GAAM,KAAM,GAAI;AAAA,EAC9B,WAAW,CAAC,IAAM,KAAM,GAAI;AAAA,EAC5B,KAAK,CAAC,KAAM,KAAM,GAAI;AAAA,EACtB,MAAM,CAAC,GAAM,KAAM,GAAI;AAAA,EACvB,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,QAAQ,CAAC,KAAM,IAAM,EAAI;AAAA,EACzB,WAAW,CAAC,IAAM,KAAM,GAAI;AAAA,EAC5B,QAAQ,CAAC,KAAM,KAAM,GAAI;AAAA,EACzB,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,QAAQ,CAAC,KAAM,KAAM,CAAI;AAAA,EACzB,aAAa,CAAC,KAAM,KAAM,EAAI;AAChC;AAUO,MAAM,yBAAyB,CACpC,UACA,QACA,WAAoB,UAC6B;AACjD,MAAI,WAAW,UAAU;AACvB,UAAMA,OAAM;AACZ,aAAS,UAAUA,IAAG;AACfA,WAAAA;AAAAA,EAAA;AAET,MAAI,UAAU;AACZ,aAAS,UAAU,IAAI;AACvB,WAAO,IAAI,WAAW;AAAA,EAAA;AAExB,QAAM,MAA8B,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AACtD,WAAS,UAAU,GAAG;AACf,SAAA;AACT;AAQO,MAAM,2BAA2B,CACtC,QACA,WAAoB,UAC6B;AACjD,UAAQ,QAAQ;AAAA,IACd,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,SAAS;AACZ,aAAO,IAAI,WAAW;AAAA,IAAA;AAAA,IAExB,KAAK,UAAU;AACN,aAAA;AAAA,IAAA;AAAA,IAET,SAAS;AACP,UAAI,UAAU;AACZ,eAAO,IAAI,WAAW;AAAA,MAAA;AAExB,aAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAAA,EAC3B;AAEJ;AAcO,MAAM,0BAA0B,CACrC,KACA,MAOI,OACiC;AACrC,MAAI,CAAC,MAAM,QAAQ,GAAG,GAAG;AACvB,UAAM,IAAI,UAAU,GAAG,GAAG,mBAAmB;AAAA,EAAA;AAEzC,QAAA;AAAA,IACJ,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,WAAW;AAAA,IACX,gBAAgB;AAAA,EAAA,IACd;AACJ,MAAI,CAAC,OAAO,SAAS,SAAS,GAAG;AAC/B,UAAM,IAAI,UAAU,GAAG,SAAS,mBAAmB;AAAA,EAAA;AAErD,MAAI,CAAC,OAAO,SAAS,SAAS,GAAG;AAC/B,UAAM,IAAI,UAAU,GAAG,SAAS,mBAAmB;AAAA,EAAA;AAErD,MAAI,CAAC,OAAO,SAAS,QAAQ,GAAG;AAC9B,UAAM,IAAI,UAAU,GAAG,QAAQ,mBAAmB;AAAA,EAAA;AAEpD,MAAI,CAAC,OAAO,SAAS,QAAQ,GAAG;AAC9B,UAAM,IAAI,UAAU,GAAG,QAAQ,mBAAmB;AAAA,EAAA;AAEpD,QAAM,IAAI,IAAI;AACV,MAAA,IAAI,aAAa,IAAI,WAAW;AAClC,UAAM,IAAI,MAAM,2BAA2B,CAAC,GAAG;AAAA,EAAA;AAEjD,MAAI,IAAI;AACR,SAAO,IAAI,GAAG;AACN,UAAA,IAAI,IAAI,CAAC;AACf,QAAI,CAAC,OAAO,SAAS,CAAC,GAAG;AACvB,YAAM,IAAI,UAAU,GAAG,CAAC,mBAAmB;AAAA,IAAA,WAClC,IAAI,QAAQ,kBAAkB,IAAI,YAAY,IAAI,WAAW;AAChE,YAAA,IAAI,WAAW,GAAG,CAAC,mBAAmB,QAAQ,QAAQ,QAAQ,GAAG;AAAA,IAAA,WAC9D,MAAM,SAAS,IAAI,KAAK,IAAI,IAAI;AACzC,YAAM,IAAI,WAAW,GAAG,CAAC,0BAA0B;AAAA,IAAA;AAErD;AAAA,EAAA;AAEE,MAAA,SAAS,MAAM,MAAM;AACvB,QAAI,KAAK,CAAC;AAAA,EAAA;AAEL,SAAA;AACT;AASO,MAAM,kBAAkB,CAC7B,KACA,KACA,OAAgB,UACK;AACrB,MAAI,CAAC,MAAM,QAAQ,GAAG,GAAG;AACvB,UAAM,IAAI,UAAU,GAAG,GAAG,mBAAmB;AAAA,EAAA,WACpC,IAAI,WAAW,MAAM;AAC9B,UAAM,IAAI,MAAM,2BAA2B,IAAI,MAAM,GAAG;AAAA,EAAA,WAC/C,CAAC,MAAM;AAChB,aAAS,KAAK,KAAK;AACjB,UAAI,wBAAwB,GAAuB;AAAA,QACjD,WAAW;AAAA,QACX,eAAe;AAAA,MAAA,CAChB;AAAA,IAAA;AAAA,EACH;AAEF,QAAM,CAAC,CAAC,MAAM,MAAM,IAAI,GAAG,CAAC,MAAM,MAAM,IAAI,GAAG,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI;AACrE,MAAI,IAAI,IAAI;AACZ,MAAI,MAAM;AACP,KAAA,IAAI,IAAI,EAAE,IAAI;AAAA,EAAA,OACV;AACL,KAAC,IAAI,IAAI,EAAE,IAAI,wBAAwB,KAAK;AAAA,MAC1C,WAAW;AAAA,MACX,eAAe;AAAA,IAAA,CAChB;AAAA,EAAA;AAEH,QAAM,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO;AAC1C,QAAM,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO;AAC1C,QAAM,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO;AACnC,SAAA,CAAC,IAAI,IAAI,EAAE;AACpB;AASO,MAAM,2BAA2B,CACtC,QACA,QACA,OAAgB,UACmB;AACnC,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,UAAM,IAAI,UAAU,GAAG,MAAM,mBAAmB;AAAA,EAAA,WACvC,OAAO,WAAW,MAAM;AACjC,UAAM,IAAI,MAAM,2BAA2B,OAAO,MAAM,GAAG;AAAA,EAAA;AAE7D,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,UAAM,IAAI,UAAU,GAAG,MAAM,mBAAmB;AAAA,EAAA,WACvC,OAAO,WAAW,MAAM;AACjC,UAAM,IAAI,MAAM,2BAA2B,OAAO,MAAM,GAAG;AAAA,EAAA;AAE7D,MAAI,IAAI;AACR,SAAO,IAAI,MAAM;AACf,QAAI,OAAO,CAAC,MAAM,QAAQ,OAAO,CAAC,MAAM,MAAM;AAC5C,aAAO,CAAC,IAAI;AACZ,aAAO,CAAC,IAAI;AAAA,IACH,WAAA,OAAO,CAAC,MAAM,MAAM;AACtB,aAAA,CAAC,IAAI,OAAO,CAAC;AAAA,IACX,WAAA,OAAO,CAAC,MAAM,MAAM;AACtB,aAAA,CAAC,IAAI,OAAO,CAAC;AAAA,IAAA;AAEtB;AAAA,EAAA;AAEF,MAAI,MAAM;AACD,WAAA,CAAC,QAAyB,MAAuB;AAAA,EAAA;AAEpD,QAAA,kBAAkB,wBAAwB,QAAyB;AAAA,IACvE,WAAW;AAAA,IACX,eAAe;AAAA,EAAA,CAChB;AACK,QAAA,kBAAkB,wBAAwB,QAAyB;AAAA,IACvE,WAAW;AAAA,IACX,eAAe;AAAA,EAAA,CAChB;AACM,SAAA,CAAC,iBAAkC,eAAgC;AAC5E;AAOa,MAAA,oBAAoB,CAAC,UAA0B;AAC1D,MAAI,CAAC,OAAO,SAAS,KAAK,GAAG;AAC3B,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA,OAC1C;AACG,YAAA,KAAK,MAAM,KAAK;AACpB,QAAA,QAAQ,KAAK,QAAQ,SAAS;AAChC,YAAM,IAAI,WAAW,GAAG,KAAK,yBAAyB,OAAO,GAAG;AAAA,IAAA;AAAA,EAClE;AAEE,MAAA,MAAM,MAAM,SAAS,GAAG;AACxB,MAAA,IAAI,WAAW,GAAG;AACpB,UAAM,IAAI,GAAG;AAAA,EAAA;AAER,SAAA;AACT;AAOa,MAAA,aAAa,CAAC,UAA0B;AAC/C,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,OAAO,MAAM;AACb,QAAA,MAAM,OAAO,KAAK,KAAK;AAC7B,QAAM,MAAM,IAAI,OAAO,KAAK,GAAG,KAAK,KAAK,KAAK;AAC9C,MAAI,CAAC,IAAI,KAAK,KAAK,GAAG;AACpB,UAAM,IAAI,YAAY,2BAA2B,KAAK,EAAE;AAAA,EAAA;AAE1D,QAAM,CAAA,EAAG,OAAO,IAAI,IAAI,MAAM,MAAM,GAAG;AACnC,MAAA;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACG,YAAA,WAAW,KAAK,IAAI;AAC1B;AAAA,IACF,KAAK;AACG,YAAA,WAAW,KAAK,IAAI;AAC1B;AAAA,IACF,KAAK;AACG,YAAA,WAAW,KAAK,IAAI;AAC1B;AAAA,IACF;AACE,YAAM,WAAW,KAAK;AAAA,EAAA;AAEnB,SAAA;AACP,MAAI,MAAM,GAAG;AACJ,WAAA;AAAA,EACE,WAAA,OAAO,GAAG,KAAK,EAAE,GAAG;AACvB,UAAA;AAAA,EAAA;AAED,SAAA;AACT;AAOa,MAAA,aAAa,CAAC,QAAgB,OAAe;AACpD,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AACnB,QAAI,CAAC,OAAO;AACF,cAAA;AAAA,IAAA,WACC,UAAU,MAAM;AACjB,cAAA;AAAA,IAAA,OACH;AACD,UAAA;AACA,UAAA,MAAM,SAAS,GAAG,GAAG;AACnB,YAAA,WAAW,KAAK,IAAI;AAAA,MAAA,OACnB;AACL,YAAI,WAAW,KAAK;AAAA,MAAA;AAEtB,UAAI,CAAC,OAAO,SAAS,CAAC,GAAG;AACvB,cAAM,IAAI,UAAU,GAAG,CAAC,0BAA0B;AAAA,MAAA;AAEpD,UAAI,IAAI,MAAM;AACJ,gBAAA;AAAA,MAAA,WACC,IAAI,GAAG;AACR,gBAAA;AAAA,MAAA,OACH;AACG,gBAAA,EAAE,QAAQ,IAAI;AAAA,MAAA;AAAA,IACxB;AAAA,EACF,OACK;AACG,YAAA;AAAA,EAAA;AAEV,SAAO,WAAW,KAAK;AACzB;AAOa,MAAA,gBAAgB,CAAC,UAA0B;AAClD,MAAA,SAAS,KAAK,GAAG;AACnB,QAAI,UAAU,IAAI;AACV,YAAA,IAAI,YAAY,wCAAwC;AAAA,IAAA;AAEhE,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE7C,MAAA,QAAQ,SAAS,OAAO,GAAG;AAC/B,MAAI,SAAS,GAAG;AACP,WAAA;AAAA,EAAA;AAET,MAAI,SAAS,SAAS;AACb,WAAA;AAAA,EAAA;AAEH,QAAA,+BAAe,IAAI;AACzB,WAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,aAAS,IAAI,KAAK,MAAO,IAAI,UAAW,OAAO,GAAG,CAAC;AAAA,EAAA;AAEjD,MAAA,SAAS,IAAI,KAAK,GAAG;AACf,YAAA,SAAS,IAAI,KAAK,IAAI;AAAA,EAAA,OACzB;AACL,YAAQ,KAAK,MAAM,QAAQ,UAAU,IAAI,IAAI;AAAA,EAAA;AAE/C,SAAO,WAAW,MAAM,QAAQ,IAAI,CAAC;AACvC;AAQO,MAAM,0BAA0B,CACrC,KACA,OAAgB,UACK;AACrB,MAAI,IAAI,IAAI;AACZ,MAAI,MAAM;AACP,KAAA,IAAI,IAAI,EAAE,IAAI;AAAA,EAAA,OACV;AACL,KAAC,IAAI,IAAI,EAAE,IAAI,wBAAwB,KAAK;AAAA,MAC1C,WAAW;AAAA,MACX,UAAU;AAAA,IAAA,CACX;AAAA,EAAA;AAEH,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AACb,QAAM,WAAW;AACjB,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,KAAK,IAAI,kBAAkB,IAAI,gBAAgB,UAAU;AAAA,EAAA,OAC7D;AACA,SAAA;AAAA,EAAA;AAEP,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,KAAK,IAAI,kBAAkB,IAAI,gBAAgB,UAAU;AAAA,EAAA,OAC7D;AACA,SAAA;AAAA,EAAA;AAEP,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,KAAK,IAAI,kBAAkB,IAAI,gBAAgB,UAAU;AAAA,EAAA,OAC7D;AACA,SAAA;AAAA,EAAA;AAEA,SAAA,CAAC,GAAG,GAAG,CAAC;AACjB;AAQO,MAAM,oBAAoB,CAC/B,KACA,OAAgB,UACK;AACrB,MAAI,CAAC,MAAM;AACT,UAAM,wBAAwB,KAAK;AAAA,MACjC,WAAW;AAAA,MACX,UAAU;AAAA,IAAA,CACX;AAAA,EAAA;AAEG,QAAA,wBAAwB,KAAK,IAAI;AACvC,QAAM,MAAM,gBAAgB,qBAAqB,KAAK,IAAI;AACnD,SAAA;AACT;AAqBO,MAAM,0BAA0B,CACrC,KACA,QAAiB,UACI;AACrB,MAAI,CAAC,GAAG,GAAG,CAAC,IAAI,wBAAwB,KAAK;AAAA,IAC3C,WAAW;AAAA,EAAA,CACZ;AACD,QAAM,WAAW,MAAM;AACvB,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,IAAI,GAAG,IAAI,UAAU,KAAK,IAAI,iBAAiB;AAAA,EAAA,OACnD;AACA,SAAA;AAAA,EAAA;AAEF,OAAA;AACL,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,IAAI,GAAG,IAAI,UAAU,KAAK,IAAI,iBAAiB;AAAA,EAAA,OACnD;AACA,SAAA;AAAA,EAAA;AAEF,OAAA;AACL,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,IAAI,GAAG,IAAI,UAAU,KAAK,IAAI,iBAAiB;AAAA,EAAA,OACnD;AACA,SAAA;AAAA,EAAA;AAEF,OAAA;AACE,SAAA;AAAA,IACL,QAAQ,KAAK,MAAM,CAAC,IAAI;AAAA,IACxB,QAAQ,KAAK,MAAM,CAAC,IAAI;AAAA,IACxB,QAAQ,KAAK,MAAM,CAAC,IAAI;AAAA,EAC1B;AACF;AAQO,MAAM,oBAAoB,CAC/B,KACA,OAAgB,UACK;AACrB,MAAI,CAAC,MAAM;AACT,UAAM,wBAAwB,KAAK;AAAA,MACjC,WAAW;AAAA,MACX,eAAe;AAAA,IAAA,CAChB;AAAA,EAAA;AAEC,MAAA,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,KAAK,IAAI;AAC7D,GAAA,GAAG,GAAG,CAAC,IAAI;AAAA,IACV;AAAA,MACE,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,MAC1B,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,MAC1B,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,IAC5B;AAAA,IACA;AAAA,EACF;AACO,SAAA,CAAC,GAAG,GAAG,CAAC;AACjB;AAwBO,MAAM,oBAAoB,CAC/B,KACA,OAAgB,UACK;AACrB,QAAM,CAAC,IAAI,IAAI,EAAE,IAAI,kBAAkB,KAAK,IAAI;AAChD,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AACf,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,QAAM,IAAI,MAAM;AACV,QAAA,KAAK,MAAM,OAAO,OAAO;AAC/B,MAAI,GAAG;AACH,MAAA,KAAK,MAAM,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC,MAAM,SAAS;AAChD,QAAA;AACA,QAAA;AAAA,EAAA,OACC;AACL,QAAK,KAAK,IAAI,KAAK,IAAI,MAAM,MAAM,CAAC,KAAM;AAC1C,QAAI,MAAM,GAAG;AACP,UAAA;AAAA,IAAA,OACC;AACL,cAAQ,KAAK;AAAA,QACX,KAAK;AACH,eAAK,IAAI,KAAK;AACd;AAAA,QACF,KAAK;AACE,eAAA,IAAI,KAAK,IAAI;AAClB;AAAA,QACF,KAAK;AAAA,QACL;AACO,eAAA,IAAI,KAAK,IAAI;AAClB;AAAA,MAAA;AAEJ,UAAK,IAAI,OAAQ;AACjB,UAAI,IAAI,GAAG;AACJ,aAAA;AAAA,MAAA;AAAA,IACP;AAAA,EACF;AAEK,SAAA,CAAC,GAAG,GAAG,CAAC;AACjB;AAQO,MAAM,oBAAoB,CAC/B,KACA,OAAgB,UACK;AACrB,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,kBAAkB,KAAK,IAAI;AAC7C,QAAM,KAAK,KAAK,IAAI,GAAG,GAAG,CAAC,IAAI;AAC/B,QAAM,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC,IAAI;AAC/B,MAAA;AACA,MAAA,KAAK,OAAO,GAAG;AACb,QAAA;AAAA,EAAA,OACC;AACJ,KAAA,CAAC,IAAI,kBAAkB,GAAG;AAAA,EAAA;AAE7B,SAAO,CAAC,GAAG,KAAK,SAAS,KAAK,OAAO;AACvC;AAQO,MAAM,sBAAsB,CACjC,KACA,OAAgB,UACK;AACrB,MAAI,CAAC,MAAM;AACT,UAAM,wBAAwB,KAAK;AAAA,MACjC,WAAW;AAAA,MACX,eAAe;AAAA,IAAA,CAChB;AAAA,EAAA;AAEH,QAAM,MAAM,gBAAgB,mBAAmB,KAAK,IAAI;AACxD,QAAM,SAAS,IAAI,IAAI,OAAK,KAAK,KAAK,CAAC,CAAC;AACpC,MAAA,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,QAAQ,IAAI;AACjE,MAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AACxB,QAAA,OAAO,KAAK,MAAM,WAAW,EAAE,QAAQ,IAAI,CAAC,IAAI,OAAO;AACzD,MAAA,SAAS,KAAK,SAAS,SAAS;AAC9B,QAAA;AACA,QAAA;AAAA,EAAA;AAEC,SAAA,CAAC,GAAG,GAAG,CAAC;AACjB;AAQO,MAAM,sBAAsB,CACjC,KACA,OAAgB,UACK;AACrB,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,oBAAoB,KAAK,IAAI;AAC/C,MAAI,GAAG;AACD,QAAA,OAAO,KAAK,MAAM,WAAW,EAAE,QAAQ,IAAI,CAAC,IAAI,OAAO;AACzD,MAAA,SAAS,KAAK,SAAS,SAAS;AAC9B,QAAA;AACA,QAAA;AAAA,EAAA,OACC;AACL,QAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,OAAO,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC;AACtE,QAAI,WAAW,EAAE,QAAQ,IAAI,CAAC,MAAM,GAAG;AACjC,UAAA;AAAA,IAAA,OACC;AACL,UAAK,KAAK,MAAM,GAAG,CAAC,IAAI,WAAY,KAAK;AACzC,UAAI,IAAI,GAAG;AACJ,aAAA;AAAA,MAAA;AAAA,IACP;AAAA,EACF;AAEK,SAAA,CAAC,GAAG,GAAG,CAAC;AACjB;AAQO,MAAM,uBAAuB,CAClC,KACA,OAAgB,UACK;AACrB,MAAI,CAAC,MAAM;AACT,UAAM,wBAAwB,KAAK;AAAA,MACjC,WAAW;AAAA,MACX,eAAe;AAAA,IAAA,CAChB;AAAA,EAAA;AAEH,QAAM,SAAS,gBAAgB,mBAAmB,KAAK,IAAI;AACrD,QAAA,MAAM,kBAAkB,QAAQ,IAAI;AACnC,SAAA;AACT;AAQO,MAAM,uBAAuB,CAClC,KACA,OAAgB,UACK;AACrB,MAAI,CAAC,MAAM;AACT,UAAM,wBAAwB,KAAK;AAAA,MACjC,WAAW;AAAA,MACX,eAAe;AAAA,IAAA,CAChB;AAAA,EAAA;AAEG,QAAA,SAAS,IAAI,IAAI,CAAC,KAAK,MAAM,MAAO,IAAI,CAAC,CAAY;AAC3D,QAAM,CAAC,IAAI,IAAI,EAAE,IAAI,OAAO;AAAA,IAAI,CAAA,QAC9B,MAAM,cAAc,KAAK,KAAK,GAAG,KAAK,MAAM,YAAY,OAAO;AAAA,EACjE;AACM,QAAA,IAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,KAAK,CAAC,GAAG,OAAO;AACzD,MAAI,GAAG;AACH,MAAA,MAAM,KAAK,MAAM,SAAS;AACxB,QAAA;AACA,QAAA;AAAA,EAAA,OACC;AACL,SAAK,KAAK,MAAM;AAChB,SAAK,KAAK,MAAM;AAAA,EAAA;AAEX,SAAA,CAAC,GAAG,GAAG,CAAC;AACjB;AAQO,MAAM,uBAAuB,CAClC,KACA,OAAgB,UACK;AACrB,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,qBAAqB,KAAK,IAAI;AAChD,MAAI,GAAG;AACH,MAAA,MAAM,KAAK,MAAM,SAAS;AACxB,QAAA;AACA,QAAA;AAAA,EAAA,OACC;AACL,QAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,OAAO,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC;AACtE,QAAK,KAAK,MAAM,GAAG,CAAC,IAAI,WAAY,KAAK;AACzC,QAAI,IAAI,GAAG;AACJ,WAAA;AAAA,IAAA;AAAA,EACP;AAEK,SAAA,CAAC,GAAG,GAAG,CAAC;AACjB;AAOa,MAAA,kBAAkB,CAAC,QAA+B;AAC7D,QAAM,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,wBAAwB,KAAK;AAAA,IACpD,OAAO;AAAA,IACP,UAAU;AAAA,EAAA,CACX;AACK,QAAA,KAAK,kBAAkB,CAAC;AACxB,QAAA,KAAK,kBAAkB,CAAC;AACxB,QAAA,KAAK,kBAAkB,CAAC;AACxB,QAAA,KAAK,kBAAkB,QAAQ,OAAO;AACxC,MAAA;AACJ,MAAI,OAAO,MAAM;AACf,UAAM,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;AAAA,EAAA,OACjB;AACL,UAAM,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAAA,EAAA;AAEtB,SAAA;AACT;AAmFa,MAAA,kBAAkB,CAAC,UAAiC;AAC3D,MAAA,SAAS,KAAK,GAAG;AACX,YAAA,MAAM,YAAY,EAAE,KAAK;AAAA,EAAA,OAC5B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,MACE,EACE,gBAAgB,KAAK,KAAK,KAC1B,gBAAgB,KAAK,KAAK,KAC1B,gBAAgB,KAAK,KAAK,KAC1B,gBAAgB,KAAK,KAAK,IAE5B;AACA,UAAM,IAAI,YAAY,2BAA2B,KAAK,EAAE;AAAA,EAAA;AAE1D,QAAM,MAAgB,CAAC;AACnB,MAAA,gBAAgB,KAAK,KAAK,GAAG;AAC/B,UAAM,CAAG,EAAA,GAAG,GAAG,CAAC,IAAI,MAAM;AAAA,MACxB;AAAA,IACF;AACI,QAAA;AAAA,MACF,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB;AAAA,IACF;AAAA,EACS,WAAA,gBAAgB,KAAK,KAAK,GAAG;AACtC,UAAM,CAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI,MAAM;AAAA,MAC/B;AAAA,IACF;AACI,QAAA;AAAA,MACF,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB,cAAc,GAAG,KAAK,GAAG,KAAK,EAAE;AAAA,IAClC;AAAA,EACS,WAAA,gBAAgB,KAAK,KAAK,GAAG;AACtC,UAAM,CAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI,MAAM;AAAA,MAC/B;AAAA,IACF;AACI,QAAA;AAAA,MACF,SAAS,GAAG,GAAG;AAAA,MACf,SAAS,GAAG,GAAG;AAAA,MACf,SAAS,GAAG,GAAG;AAAA,MACf,cAAc,KAAK;AAAA,IACrB;AAAA,EAAA,OACK;AACL,UAAM,CAAG,EAAA,GAAG,GAAG,CAAC,IAAI,MAAM;AAAA,MACxB;AAAA,IACF;AACA,QAAI,KAAK,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,EAAA;AAE3D,SAAA;AACT;AAOa,MAAA,wBAAwB,CAAC,UAAiC;AACrE,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,gBAAgB,KAAK;AAC3C,QAAA,CAAC,GAAG,GAAG,CAAC,IAAI,wBAAwB,CAAC,IAAI,IAAI,EAAE,GAAG,IAAI;AAC5D,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAOa,MAAA,kBAAkB,CAAC,UAAiC;AAC/D,QAAM,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,sBAAsB,KAAK;AACpD,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AACtE,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAQO,MAAM,WAAW,CACtB,OACA,MAAe,OACkC;AAC7C,MAAA,SAAS,KAAK,GAAG;AACX,YAAA,MAAM,YAAY,EAAE,KAAK;AAAA,EAAA,OAC5B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,EAAE,SAAS,IAAI,WAAW,MAAU,IAAA;AAC1C,QAAM,MAAM,IAAI,OAAO,iBAAiB,OAAO,IAAI,WAAW,WAAW;AACzE,MAAI,CAAC,IAAI,KAAK,KAAK,GAAG;AACd,UAAA,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAEL,QAAA,SAAS,GAAG,GAAG;AACV,aAAA;AAAA,IAAA;AAEF,WAAA;AAAA,EAAA;AAET,QAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,GAAG;AAC/B,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,IAC3B,QAAQ,SAAS,GAAG,EACpB,MAAM,KAAK;AACd,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,SAAS,GAAG,GAAG;AACf,UAAA,WAAW,EAAE,IAAI,UAAW;AAAA,IAAA,OAC5B;AACL,UAAI,WAAW,EAAE;AAAA,IAAA;AAEf,QAAA,KAAK,IAAI,KAAK,IAAI,iBAAiB,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,EAAA;AAE7D,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,SAAS,GAAG,GAAG;AACf,UAAA,WAAW,EAAE,IAAI,UAAW;AAAA,IAAA,OAC5B;AACL,UAAI,WAAW,EAAE;AAAA,IAAA;AAEf,QAAA,KAAK,IAAI,KAAK,IAAI,iBAAiB,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,EAAA;AAE7D,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,SAAS,GAAG,GAAG;AACf,UAAA,WAAW,EAAE,IAAI,UAAW;AAAA,IAAA,OAC5B;AACL,UAAI,WAAW,EAAE;AAAA,IAAA;AAEf,QAAA,KAAK,IAAI,KAAK,IAAI,iBAAiB,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,EAAA;AAEvD,QAAA,QAAQ,WAAW,EAAE;AACpB,SAAA,CAAC,OAAO,GAAG,GAAG,GAAG,WAAW,WAAW,OAAO,OAAO,OAAO,KAAK;AAC1E;AAQO,MAAM,WAAW,CACtB,OACA,MAAe,OACkC;AAC7C,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,EAAE,SAAS,IAAI,WAAW,MAAU,IAAA;AAC1C,MAAI,CAAC,QAAQ,KAAK,KAAK,GAAG;AAClB,UAAA,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAEL,QAAA,SAAS,GAAG,GAAG;AACV,aAAA;AAAA,IAAA;AAEF,WAAA;AAAA,EAAA;AAET,QAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,OAAO;AACnC,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,IAC3B,QAAQ,SAAS,GAAG,EACpB,MAAM,KAAK;AACd,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACL,QAAI,WAAW,EAAE;AAAA,EAAA;AAEnB,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,KAAK,IAAI,KAAK,IAAI,WAAW,EAAE,GAAG,CAAC,GAAG,OAAO;AAAA,EAAA;AAEnD,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,KAAK,IAAI,KAAK,IAAI,WAAW,EAAE,GAAG,CAAC,GAAG,OAAO;AAAA,EAAA;AAE7C,QAAA,QAAQ,WAAW,EAAE;AAC3B,MAAI,WAAW,OAAO;AACb,WAAA;AAAA,MACL;AAAA,MACA,OAAO,OAAO,KAAK;AAAA,MACnB,OAAO,OAAO,KAAK;AAAA,MACnB,OAAO,OAAO,KAAK;AAAA,MACnB,OAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EAAA;AAEF,MAAK,IAAI,MAAO;AACX,OAAA;AACL,QAAM,KAAM,IAAI,UAAW,KAAK,IAAI,GAAG,IAAI,CAAC;AAC5C,QAAM,KAAK,IAAI;AACT,QAAA,MAAM,IAAI,KAAK;AACf,QAAA,MAAM,IAAI,KAAK;AACrB,QAAM,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,MAAM,QAAQ,UAAU,IAAI,CAAC,CAAC;AAC5E,QAAM,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,MAAM,QAAQ,UAAU,IAAI,CAAC,CAAC;AAC5E,QAAM,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,MAAM,QAAQ,UAAU,IAAI,CAAC,CAAC;AACrE,SAAA;AAAA,IACL;AAAA,IACA,KAAK,IAAI,KAAK,IAAI,iBAAiB,IAAI,SAAS,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,IACjE,KAAK,IAAI,KAAK,IAAI,iBAAiB,IAAI,SAAS,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,IACjE,KAAK,IAAI,KAAK,IAAI,iBAAiB,IAAI,SAAS,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,IACjE;AAAA,EACF;AACF;AAQO,MAAM,WAAW,CACtB,OACA,MAAe,OACkC;AAC7C,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,EAAE,SAAS,IAAI,WAAW,MAAU,IAAA;AAC1C,MAAI,CAAC,QAAQ,KAAK,KAAK,GAAG;AAClB,UAAA,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAEL,QAAA,SAAS,GAAG,GAAG;AACV,aAAA;AAAA,IAAA;AAEF,WAAA;AAAA,EAAA;AAET,QAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,OAAO;AACnC,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,IAC3B,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,MAAI,GAAG,IAAI;AACX,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACL,QAAI,WAAW,EAAE;AAAA,EAAA;AAEnB,MAAI,OAAO,MAAM;AACV,SAAA;AAAA,EAAA,OACA;AACA,SAAA,KAAK,IAAI,KAAK,IAAI,WAAW,EAAE,GAAG,CAAC,GAAG,OAAO,IAAI;AAAA,EAAA;AAExD,MAAI,OAAO,MAAM;AACV,SAAA;AAAA,EAAA,OACA;AACA,SAAA,KAAK,IAAI,KAAK,IAAI,WAAW,EAAE,GAAG,CAAC,GAAG,OAAO,IAAI;AAAA,EAAA;AAElD,QAAA,QAAQ,WAAW,EAAE;AAC3B,MAAI,WAAW,OAAO;AACb,WAAA;AAAA,MACL;AAAA,MACA,OAAO,OAAO,KAAK;AAAA,MACnB,OAAO,OAAO,KAAK,KAAK;AAAA,MACxB,OAAO,OAAO,KAAK,KAAK;AAAA,MACxB,OAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EAAA;AAEE,MAAA,KAAK,MAAM,GAAG;AAChB,UAAM,IAAI,iBAAkB,MAAM,KAAK,MAAO,SAAS,GAAG;AAC1D,WAAO,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;AAAA,EAAA;AAEzB,QAAA,UAAU,IAAI,KAAK,MAAM;AAC3B,MAAA,CAAG,EAAA,GAAG,GAAG,CAAC,IAAI,SAAS,OAAO,CAAC,UAAU;AAC7C,MAAI,kBAAkB,IAAI,SAAS,MAAM,SAAS,GAAG;AACrD,MAAI,kBAAkB,IAAI,SAAS,MAAM,SAAS,GAAG;AACrD,MAAI,kBAAkB,IAAI,SAAS,MAAM,SAAS,GAAG;AAC9C,SAAA;AAAA,IACL;AAAA,IACA,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,OAAO;AAAA,IAChC,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,OAAO;AAAA,IAChC,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,OAAO;AAAA,IAChC;AAAA,EACF;AACF;AASO,MAAM,WAAW,CACtB,OACA,MAAe,OACkC;AAC7C,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,EAAE,SAAS,IAAI,WAAW,MAAU,IAAA;AAC1C,MAAI,CAAC,QAAQ,KAAK,KAAK,GAAG;AAClB,UAAA,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAEL,QAAA,SAAS,GAAG,GAAG;AACV,aAAA;AAAA,IAAA;AAEF,WAAA;AAAA,EAAA;AAET,QAAM,WAAW;AACjB,QAAM,WAAW;AACjB,QAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,OAAO;AACnC,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,IAC3B,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,SAAS,GAAG,GAAG;AACpB,UAAI,WAAW,EAAE;AACjB,UAAI,IAAI,SAAS;AACX,YAAA;AAAA,MAAA;AAAA,IACN,OACK;AACL,UAAI,WAAW,EAAE;AAAA,IAAA;AAEnB,QAAI,IAAI,GAAG;AACL,UAAA;AAAA,IAAA;AAAA,EACN;AAEF,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,WAAW,WAAW,EAAE;AAAA,EAAA;AAElE,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,WAAW,WAAW,EAAE;AAAA,EAAA;AAE5D,QAAA,QAAQ,WAAW,EAAE;AACvB,MAAA,SAAS,KAAK,MAAM,GAAG;AAClB,WAAA;AAAA,MACL;AAAA,MACA,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EAAA;AAEI,QAAA,MAAM,IAAI,OAAO;AACjB,QAAA,KAAK,IAAI,QAAQ;AACjB,QAAA,KAAK,KAAK,IAAI;AACpB,QAAM,QAAQ,KAAK,IAAI,IAAI,QAAQ;AACnC,QAAM,QAAQ,KAAK,IAAI,IAAI,QAAQ;AACnC,QAAM,QAAQ,KAAK,IAAI,IAAI,QAAQ;AACnC,QAAM,MAAM;AAAA,IACV,QAAQ,cAAc,SAAS,KAAK,QAAQ,OAAO;AAAA,IACnD,IAAI,WAAW,QAAQ,IAAI;AAAA,IAC3B,QAAQ,cAAc,SAAS,KAAK,QAAQ,OAAO;AAAA,EACrD;AACA,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI;AAAA,IACpB,CAACC,MAAK,MAAMA,OAAO,IAAI,CAAC;AAAA,EAC1B;AACO,SAAA;AAAA,IACL;AAAA,IACA,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB;AAAA,EACF;AACF;AAUO,MAAM,WAAW,CACtB,OACA,MAAe,OACkC;AAC7C,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,EAAE,SAAS,IAAI,WAAW,MAAU,IAAA;AAC1C,MAAI,CAAC,QAAQ,KAAK,KAAK,GAAG;AAClB,UAAA,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAEL,QAAA,SAAS,GAAG,GAAG;AACV,aAAA;AAAA,IAAA;AAEF,WAAA;AAAA,EAAA;AAET,QAAM,WAAW;AACjB,QAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,OAAO;AACnC,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,IAC3B,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACL,QAAI,WAAW,EAAE;AACjB,QAAI,IAAI,GAAG;AACL,UAAA;AAAA,IAAA;AAAA,EACN;AAEF,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,WAAW,WAAW,EAAE;AAAA,EAAA;AAElE,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACL,QAAI,WAAW,EAAE;AAAA,EAAA;AAEb,QAAA,QAAQ,WAAW,EAAE;AACvB,MAAA,SAAS,KAAK,MAAM,GAAG;AAClB,WAAA;AAAA,MACL;AAAA,MACA,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EAAA;AAEF,QAAM,IAAI,IAAI,KAAK,IAAK,IAAI,KAAK,KAAM,QAAQ;AAC/C,QAAM,IAAI,IAAI,KAAK,IAAK,IAAI,KAAK,KAAM,QAAQ;AAC/C,QAAM,CAAG,EAAA,GAAG,GAAG,CAAC,IAAI,SAAS,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;AAC3C,SAAA;AAAA,IACL;AAAA,IACA,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB;AAAA,EACF;AACF;AAUO,MAAM,aAAa,CACxB,OACA,MAAe,OACkC;AAC7C,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,EAAE,SAAS,IAAI,WAAW,MAAU,IAAA;AAC1C,MAAI,CAAC,UAAU,KAAK,KAAK,GAAG;AACpB,UAAA,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAEL,QAAA,SAAS,GAAG,GAAG;AACV,aAAA;AAAA,IAAA;AAEF,WAAA;AAAA,EAAA;AAET,QAAM,WAAW;AACjB,QAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,SAAS;AACrC,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,IAC3B,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,UAAU,WAAW,EAAE;AAC/D,QAAI,IAAI,GAAG;AACL,UAAA;AAAA,IAAA;AAAA,EACN;AAEF,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EACK,WAAA,GAAG,SAAS,GAAG,GAAG;AACtB,QAAA,WAAW,EAAE,IAAI,WAAY;AAAA,EAAA,OAC7B;AACL,QAAI,WAAW,EAAE;AAAA,EAAA;AAEnB,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EACK,WAAA,GAAG,SAAS,GAAG,GAAG;AACtB,QAAA,WAAW,EAAE,IAAI,WAAY;AAAA,EAAA,OAC7B;AACL,QAAI,WAAW,EAAE;AAAA,EAAA;AAEb,QAAA,QAAQ,WAAW,EAAE;AACvB,MAAA,SAAS,KAAK,MAAM,GAAG;AAClB,WAAA;AAAA,MACL;AAAA,MACA,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EAAA;AAEF,QAAM,MAAM,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,CAAC;AACpD,QAAA,SAAS,IAAI,IAAI,CAAA,MAAK,KAAK,IAAI,GAAG,QAAQ,CAAC;AAC3C,QAAA,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,QAAQ,IAAI;AAC1D,SAAA;AAAA,IACL;AAAA,IACA,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB;AAAA,EACF;AACF;AAUO,MAAM,aAAa,CACxB,OACA,MAAe,OACkC;AAC7C,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,EAAE,SAAS,IAAI,WAAW,MAAU,IAAA;AAC1C,MAAI,CAAC,UAAU,KAAK,KAAK,GAAG;AACpB,UAAA,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAEL,QAAA,SAAS,GAAG,GAAG;AACV,aAAA;AAAA,IAAA;AAEF,WAAA;AAAA,EAAA;AAET,QAAM,WAAW;AACjB,QAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,SAAS;AACrC,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,IAC3B,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,UAAU,WAAW,EAAE;AAC/D,QAAI,IAAI,GAAG;AACL,UAAA;AAAA,IAAA;AAAA,EACN;AAEF,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,SAAS,GAAG,GAAG;AACf,UAAA,WAAW,EAAE,IAAI,WAAY;AAAA,IAAA,OAC7B;AACL,UAAI,WAAW,EAAE;AAAA,IAAA;AAEnB,QAAI,IAAI,GAAG;AACL,UAAA;AAAA,IAAA;AAAA,EACN;AAEF,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACL,QAAI,WAAW,EAAE;AAAA,EAAA;AAEb,QAAA,QAAQ,WAAW,EAAE;AACvB,MAAA,SAAS,KAAK,MAAM,GAAG;AAClB,WAAA;AAAA,MACL;AAAA,MACA,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EAAA;AAEF,QAAM,IAAI,IAAI,KAAK,IAAK,IAAI,KAAK,KAAM,QAAQ;AAC/C,QAAM,IAAI,IAAI,KAAK,IAAK,IAAI,KAAK,KAAM,QAAQ;AAC/C,QAAM,MAAM,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,CAAC;AACpD,QAAA,SAAS,IAAI,IAAI,CAAA,OAAM,KAAK,IAAI,IAAI,QAAQ,CAAC;AAC7C,QAAA,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,QAAQ,IAAI;AAC1D,SAAA;AAAA,IACL;AAAA,IACA,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB;AAAA,EACF;AACF;AAUO,MAAM,iBAAiB,CAC5B,OACA,MAAe,OACkC;AAC7C,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,aAAa,IAAI,MAAM,OAAO,SAAS,IAAI,WAAW,MAAA,IAAU;AACxE,MAAI,CAAC,aAAa,KAAK,KAAK,GAAG;AACvB,UAAA,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAEL,QAAA,SAAS,GAAG,GAAG;AACV,aAAA;AAAA,IAAA;AAEF,WAAA;AAAA,EAAA;AAET,QAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,YAAY;AACxC,MAAI,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,IAC7B,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,OAAO;AACX,SAAA;AAAA,EAAA;AAEP,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,UAAU,WAAW,EAAE;AAAA,EAAA;AAEjE,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,UAAU,WAAW,EAAE;AAAA,EAAA;AAEjE,MAAI,OAAO,MAAM;AACX,QAAA;AAAA,EAAA,OACC;AACD,QAAA,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,UAAU,WAAW,EAAE;AAAA,EAAA;AAE3D,QAAA,QAAQ,WAAW,EAAE;AAC3B,MAAI,SAAS,KAAK,MAAM,KAAM,WAAW,WAAW,OAAO,YAAa;AAC/D,WAAA;AAAA,MACL;AAAA,MACA,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EAAA;AAEF,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AAER,MAAI,OAAO,eAAe;AACvB,KAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,CAAC;AAC1D,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAChE,WAES,OAAO,cAAc;AAC9B,UAAM,YAAY,wBAAwB;AAAA,MACxC,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IAAA,CACL;AACD,KAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,kBAAkB,SAAS;AACvD,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAChE,WAES,OAAO,WAAW;AAC3B,UAAM,QAAQ;AACd,UAAM,OAAO;AACb,UAAM,WAAW;AACjB,UAAM,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,CAAK,MAAA;AACzB,UAAA;AACA,UAAA,IAAI,OAAO,WAAW,KAAK;AAC7B,aAAK,KAAK,WAAW;AAAA,MAAA,OAChB;AACL,aAAK,KAAK,KAAK,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ;AAAA,MAAA;AAE9C,aAAA;AAAA,IAAA,CACR;AACD,KAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,uBAAuB,GAAG;AACtD,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAChE,WAES,OAAO,WAAW;AAC3B,UAAM,UAAU,MAAM;AACtB,UAAM,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,CAAK,MAAA;AAC7B,YAAM,KAAK,KAAK,IAAI,GAAG,OAAO;AACvB,aAAA;AAAA,IAAA,CACR;AACD,KAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,GAAG;AAClD,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAChE,WAES,OAAO,gBAAgB;AAChC,UAAM,eAAe;AACrB,UAAM,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,CAAK,MAAA;AACzB,UAAA;AACA,UAAA,IAAI,KAAK,MAAM,MAAM;AAClB,aAAA,KAAK,IAAI,GAAG,YAAY;AAAA,MAAA,OACxB;AACL,aAAK,IAAI;AAAA,MAAA;AAEJ,aAAA;AAAA,IAAA,CACR;AACD,KAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,4BAA4B,GAAG;AAC3D,QAAI,CAAC,KAAK;AACP,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAGvD,WAAA,wBAAwB,KAAK,EAAE,GAAG;AAC3C,KAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;AACpB,QAAI,OAAO,WAAW;AACpB,UAAI,CAAC,KAAK;AACP,SAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,MAAA;AAAA,eAEjD,KAAK;AACb,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAChE,OAEK;AACL,KAAC,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,IAAI,SAAS,IAAI,SAAS,IAAI,OAAO,CAAC;AACrE,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAChE;AAEK,SAAA;AAAA,IACL,MAAM,YAAY;AAAA,IAClB,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB,WAAW,WAAW,OAAO,OAAO,KAAK;AAAA,EAC3C;AACF;AAUO,MAAM,kBAAkB,CAC7B,OACA,MAAe,OACkC;AAC7C,MAAA,SAAS,KAAK,GAAG;AACX,YAAA,MAAM,YAAY,EAAE,KAAK;AAAA,EAAA,OAC5B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,EAAE,MAAM,OAAO,SAAS,IAAI,WAAW,UAAU;AACvD,MAAI,CAAC,UAAU,KAAK,KAAK,GAAG;AACpB,UAAA,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAEL,QAAA,SAAS,GAAG,GAAG;AACV,aAAA;AAAA,IAAA;AAEF,WAAA;AAAA,EAAA;AAET,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,QAAQ;AAER,MAAA,YAAY,KAAK,KAAK,GAAG;AAC3B,QAAI,WAAW,UAAU;AACvB,aAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAE3B,QAAI,WAAW,UAAU;AAChB,aAAA;AAAA,IAAA;AAAA,EAGA,WAAA,WAAW,KAAK,KAAK,GAAG;AACjC,QAAI,OAAO,UAAU,eAAe,KAAK,cAAc,KAAK,GAAG;AAC7D,UAAI,WAAW,UAAU;AAChB,eAAA;AAAA,MAAA;AAET,YAAM,CAAC,GAAG,GAAG,CAAC,IAAI,aAChB,KACF;AACQ,cAAA;AACR,UAAI,WAAW,UAAU;AACvB,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;AAAA,MAAA;AAE9B,OAAA,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC7C,UAAI,KAAK;AACN,SAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,MAAA;AAAA,IAChE,OACK;AACL,cAAQ,QAAQ;AAAA,QACd,KAAK,UAAU;AACT,cAAA,YAAY,UAAU,eAAe;AACvC,mBAAO,IAAI,WAAW;AAAA,UAAA;AAExB,iBAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,QAAA;AAAA,QAE3B,KAAK,UAAU;AACb,cAAI,UAAU,eAAe;AACpB,mBAAA;AAAA,UAAA;AAEF,iBAAA;AAAA,QAAA;AAAA,QAET,KAAK,SAAS;AACZ,cAAI,UAAU,eAAe;AAC3B,mBAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,UAAA;AAE3B,iBAAO,IAAI,WAAW;AAAA,QAAA;AAAA,MAExB;AAAA,IACF;AAAA,EAGO,WAAA,MAAM,CAAC,MAAM,KAAK;AACvB,QAAA,SAAS,KAAK,MAAM,GAAG;AACnB,YAAA,MAAM,gBAAgB,KAAK;AAC1B,aAAA,CAAC,OAAO,GAAG,GAAG;AAAA,IAAA;AAEvB,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AACxC,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAGvD,WAAA,MAAM,WAAW,KAAK,GAAG;AAC9B,QAAA,SAAS,KAAK,MAAM,GAAG;AAClB,aAAA,SAAS,OAAO,GAAG;AAAA,IAAA;AAE5B,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,KAAK;AACnC,QAAI,CAAC,KAAK;AACP,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAGvD,WAAA,MAAM,WAAW,KAAK,GAAG;AAC9B,QAAA,SAAS,KAAK,MAAM,GAAG;AAClB,aAAA,SAAS,OAAO,GAAG;AAAA,IAAA;AAE5B,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,KAAK;AACnC,QAAI,CAAC,KAAK;AACP,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAGvD,WAAA,MAAM,WAAW,OAAO,GAAG;AAChC,QAAA,SAAS,KAAK,MAAM,GAAG;AAClB,aAAA,WAAW,OAAO,GAAG;AAAA,IAAA;AAE9B,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,KAAK;AACrC,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAGvD,WAAA,MAAM,WAAW,OAAO,GAAG;AAChC,QAAA,SAAS,KAAK,MAAM,GAAG;AAClB,aAAA,WAAW,OAAO,GAAG;AAAA,IAAA;AAE9B,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,KAAK;AACrC,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAChE,OACK;AACL,QAAI,GAAG,GAAG;AAEN,QAAA,MAAM,WAAW,KAAK,GAAG;AAC3B,OAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,KAAK;AAAA,IAE1B,WAAA,MAAM,WAAW,KAAK,GAAG;AAClC,OAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,KAAK;AAAA,IAAA,OAE9B;AACJ,OAAE,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,IAAA;AAEtC,QAAA,SAAS,KAAK,MAAM,GAAG;AACzB,aAAO,CAAC,OAAO,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK;AAAA,IAAA;AAElE,KAAA,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,GAAG,CAAC,CAAC;AACvC,QAAI,KAAK;AACN,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAChE;AAEK,SAAA;AAAA,IACL,MAAM,YAAY;AAAA,IAClB,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB;AAAA,EACF;AACF;AASO,MAAM,oBAAoB,CAC/B,OACA,MAAe,OACkC;AAC7C,MAAA,SAAS,KAAK,GAAG;AACX,YAAA,MAAM,YAAY,EAAE,KAAK;AAAA,EAAA,OAC5B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,EAAE,aAAa,IAAI,SAAS,IAAI,WAAW,UAAU;AAC3D,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AAChB,aAAA;AAAA,IAAA;AAET,UAAM,aAAa,aAAa;AAC5B,QAAA,SAAS,UAAU,GAAG;AACjB,aAAA;AAAA,IAAA;AAEF,WAAA;AAAA,EAAA;AAET,MAAI,CAAC,UAAU,KAAK,KAAK,GAAG;AACpBD,UAAAA,OAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAIA,gBAAe,YAAY;AAC7B,eAAS,UAAU,IAAI;AAChBA,aAAAA;AAAAA,IAAA;AAET,aAAS,UAAUA,IAAG;AAClB,QAAA,SAASA,IAAG,GAAG;AACVA,aAAAA;AAAAA,IAAA;AAEFA,WAAAA;AAAAA,EAAA;AAET,MAAI,KAAK;AACT,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,QAAQ;AAER,MAAA,YAAY,KAAK,KAAK,GAAG;AAC3B,QAAI,WAAW,UAAU;AACvB,eAAS,UAAU,KAAK;AACjB,aAAA;AAAA,IAAA;AAAA,EAGA,WAAA,WAAW,KAAK,KAAK,GAAG;AACjC,QAAI,OAAO,UAAU,eAAe,KAAK,cAAc,KAAK,GAAG;AAC7D,UAAI,WAAW,UAAU;AACvB,iBAAS,UAAU,KAAK;AACjB,eAAA;AAAA,MAAA;AAET,OAAC,GAAG,GAAG,CAAC,IAAI,aACV,KACF;AACQ,cAAA;AAAA,IAAA,OACH;AACL,cAAQ,QAAQ;AAAA,QACd,KAAK,UAAU;AACb,cAAI,UAAU,eAAe;AAC3B,qBAAS,UAAU,KAAK;AACjB,mBAAA;AAAA,UAAA;AAET,gBAAMA,OAAM;AACZ,mBAAS,UAAUA,IAAG;AACfA,iBAAAA;AAAAA,QAAA;AAAA,QAET,KAAK,SAAS;AACZ,cAAI,UAAU,eAAe;AAC3B,kBAAMA,OAA8B,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AACtD,qBAAS,UAAUA,IAAG;AACfA,mBAAAA;AAAAA,UAAA;AAET,mBAAS,UAAU,IAAI;AACvB,iBAAO,IAAI,WAAW;AAAA,QAAA;AAAA,QAExB,KAAK;AAAA,QACL,SAAS;AACH,cAAA,YAAY,UAAU,eAAe;AACvC,qBAAS,UAAU,IAAI;AACvB,mBAAO,IAAI,WAAW;AAAA,UAAA;AAExB,gBAAMA,OAA8B,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AACtD,mBAAS,UAAUA,IAAG;AACfA,iBAAAA;AAAAA,QAAA;AAAA,MACT;AAAA,IACF;AAAA,EAGO,WAAA,MAAM,CAAC,MAAM,KAAK;AAC3B,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAAA,EAE/B,WAAA,MAAM,WAAW,KAAK,GAAG;AACjC,KAAE,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,EAE/B,WAAA,MAAM,WAAW,KAAK,GAAG;AACjC,KAAE,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,EAE/B,WAAA,cAAc,KAAK,KAAK,GAAG;AACpC,QAAI,GAAG,GAAG;AACN,QAAA,MAAM,WAAW,KAAK,GAAG;AAC1B,OAAA,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,IAAA,OACrC;AACJ,OAAA,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,IAAA;AAExC,QAAA,SAAS,KAAK,MAAM,GAAG;AACzB,YAAMA,OAA8B,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK;AACvD,eAAS,UAAUA,IAAG;AACfA,aAAAA;AAAAA,IAAA;AAER,KAAA,GAAG,GAAG,CAAC,IAAI,qBAAqB,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,EAEjC,WAAA,gBAAgB,KAAK,KAAK,GAAG;AACtC,QAAI,GAAG,GAAG;AACN,QAAA,MAAM,WAAW,OAAO,GAAG;AAC5B,OAAA,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,OAAO,GAAG;AAAA,IAAA,OACvC;AACJ,OAAA,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,OAAO,GAAG;AAAA,IAAA;AAE1C,QAAA,SAAS,KAAK,MAAM,GAAG;AACzB,YAAMA,OAA8B,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK;AACvD,eAAS,UAAUA,IAAG;AACfA,aAAAA;AAAAA,IAAA;AAER,KAAA,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,EAAA,OAElC;AACJ,KAAE,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,EAAA;AAEtC,MAAA,WAAW,WAAW,eAAe,QAAQ;AAC/C,UAAMA,OAA8B;AAAA,MAClC;AAAA,MACA,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ;AAAA,IACF;AACA,aAAS,UAAUA,IAAG;AACfA,WAAAA;AAAAA,EAAA;AAET,QAAM,MAA8B;AAAA,IAClC;AAAA,IACA,KAAK,MAAM,CAAC;AAAA,IACZ,KAAK,MAAM,CAAC;AAAA,IACZ,KAAK,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,WAAS,UAAU,GAAG;AACf,SAAA;AACT;AAQO,MAAM,mBAAmB,CAC9B,OACA,MAAe,OACkC;AAC7C,MAAA,SAAS,KAAK,GAAG;AACX,YAAA,MAAM,YAAY,EAAE,KAAK;AAAA,EAAA,OAC5B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,EAAE,aAAa,IAAI,SAAS,IAAI,WAAW,UAAU;AAC3D,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AAChB,aAAA;AAAA,IAAA;AAET,UAAM,aAAa,aAAa;AAC5B,QAAA,SAAS,UAAU,GAAG;AACjB,aAAA;AAAA,IAAA;AAEF,WAAA;AAAA,EAAA;AAET,MAAI,CAAC,aAAa,KAAK,KAAK,GAAG;AACvBA,UAAAA,OAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAIA,gBAAe,YAAY;AAC7B,eAAS,UAAU,IAAI;AAChBA,aAAAA;AAAAA,IAAA;AAET,aAAS,UAAUA,IAAG;AAClB,QAAA,SAASA,IAAG,GAAG;AACVA,aAAAA;AAAAA,IAAA;AAEFA,WAAAA;AAAAA,EAAA;AAET,QAAM,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI;AAAA,IAC3B;AAAA,IACA;AAAA,EACF;AACA,MAAI,SAAS,KAAK,MAAM,KAAM,WAAW,WAAW,OAAO,YAAa;AACtE,UAAMA,OAA8B,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE;AACvD,aAAS,UAAUA,IAAG;AACfA,WAAAA;AAAAA,EAAA;AAET,QAAM,IAAI,WAAW,GAAG,EAAE,EAAE;AAC5B,QAAM,IAAI,WAAW,GAAG,EAAE,EAAE;AAC5B,QAAM,IAAI,WAAW,GAAG,EAAE,EAAE;AAC5B,QAAM,QAAQ,WAAW,GAAG,EAAE,EAAE;AAC1B,QAAA,CAAC,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AACnD,QAAM,MAA8B,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;AAC1D,WAAS,UAAU,GAAG;AACf,SAAA;AACT;AAQO,MAAM,0BAA0B,CACrC,OACA,MAGI,OAC2B;AAC3B,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,EAAE,aAAa,IAAI,SAAS,GAAO,IAAA;AACzC,MAAI,KAAK;AACT,MAAI,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG;AAC1B,MAAI,WAAW,SAAS;AAClB,QAAA;AACA,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,eAAe,OAAO,GAAG;AAAA,IAAA,OAC1B;AACC,YAAA,gBAAgB,OAAO,GAAG;AAAA,IAAA;AAElC,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAET,KAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI;AACvB,QAAI,OAAO,YAAY;AACrB,aAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,IAAA;AAEvB,KAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,EACvD,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,UAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,YAAY;AAClC,UAAA,CAACE,GAAE,IAAI,IACV,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,QAAIA,QAAO,eAAe;AACxB,OAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,iBAAiB,OAAO;AAAA,QAC3C,QAAQ;AAAA,MAAA,CACT;AAAA,IAAA,OACI;AACL,OAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AACxC,OAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAClE,OACK;AACL,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AACzC,KAAA,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,EAAA;AAE3D,SAAA;AAAA,IACL,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,IAC1B,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,IAC1B,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,IAC1B;AAAA,EACF;AACF;AASO,MAAM,oBAAoB,CAC/B,OACA,MAAe,OACgB;AAC3B,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,SAAS,GAAA,IAAO;AACpB,MAAA,GAAG,GAAG,GAAG;AACb,MAAI,WAAW,SAAS;AAClB,QAAA;AACA,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,iBAAiB,OAAO,GAAG;AAAA,IAAA,OAC5B;AACC,YAAA,kBAAkB,OAAO,GAAG;AAAA,IAAA;AAEpC,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACZ,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,UAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,YAAY;AAClC,UAAA,CAAC,EAAE,IAAI,IACV,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,QAAI,OAAO,QAAQ;AACjB,OAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,iBAAiB,OAAO;AAAA,QAC3C,QAAQ;AAAA,MAAA,CACT;AACI,WAAA;AACA,WAAA;AACA,WAAA;AAAA,IAAA,OACA;AACL,OAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,iBAAiB,KAAK;AAAA,IAAA;AAAA,EAEpC,WAAA,qBAAqB,KAAK,KAAK,GAAG;AAC3C,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI,wBAAwB,KAAK;AAC/C,KAAA,GAAG,GAAG,CAAC,IAAI,wBAAwB,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,EAAA,OACxC;AACL,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,kBAAkB,OAAO;AAAA,MAC5C,QAAQ;AAAA,IAAA,CACT;AAAA,EAAA;AAEH,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAQO,MAAM,oBAAoB,CAC/B,OACA,MAAe,OACgB;AAC3B,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,EAAE,MAAM,OAAO,SAAS,GAAO,IAAA;AACjC,MAAA,GAAG,GAAG,GAAG;AACb,MAAI,WAAW,SAAS;AAClB,QAAA;AACA,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,eAAe,OAAO,GAAG;AAAA,IAAA,OAC1B;AACC,YAAA,gBAAgB,OAAO,GAAG;AAAA,IAAA;AAElC,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACZ,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,UAAM,CAAG,EAAA,GAAG,IAAI,MAAM,MAAM,YAAY;AAClC,UAAA,CAAC,EAAE,IAAI,IACV,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,QAAI,KAAK;AACP,UAAI,OAAO,WAAW;AACpB,SAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,iBAAiB,OAAO;AAAA,UAC3C,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA,OACI;AACL,SAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,UACnB;AAAA,UACA;AAAA,QACF;AAAA,MAAA;AAAA,IAEO,WAAA,iBAAiB,KAAK,EAAE,GAAG;AACpC,OAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,iBAAiB,OAAO;AAAA,QAC3C,QAAQ;AAAA,MAAA,CACT;AAAA,IAAA,OACI;AACL,OAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AAAA,IAAA;AAAA,EAC3C,OACK;AACJ,KAAE,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,OAAO,GAAG;AAAA,EAAA;AAEjD,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAQO,MAAM,oBAAoB,CAC/B,OACA,MAAe,OAC4D;AACvE,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,SAAS,GAAA,IAAO;AACpB,MAAA,GAAG,GAAG,GAAG;AACT,MAAA,QAAQ,KAAK,KAAK,GAAG;AACvB,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO;AAAA,MACnC,QAAQ;AAAA,IAAA,CACT;AACD,QAAI,WAAW,OAAO;AACpB,aAAO,CAAC,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK;AAAA,IAAA;AAE5D,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EAAA;AAExB,MAAI,GAAG,GAAG;AACV,MAAI,WAAW,SAAS;AAClB,QAAA;AACA,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,eAAe,OAAO,GAAG;AAAA,IAAA,OAC1B;AACC,YAAA,gBAAgB,OAAO,GAAG;AAAA,IAAA;AAElC,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACZ,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AAAA,EAAA,OACpC;AACL,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAAA,EAAA;AAE3C,GAAA,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC7C,MAAI,WAAW,OAAO;AACpB,WAAO,CAAC,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK;AAAA,EAAA;AAErD,SAAA,CAAC,WAAW,WAAW,MAAM,IAAI,OAAO,GAAG,GAAG,GAAG,KAAK;AAC/D;AAQO,MAAM,oBAAoB,CAC/B,OACA,MAAe,OAC4D;AACvE,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,SAAS,GAAA,IAAO;AACpB,MAAA,GAAG,GAAG,GAAG;AACT,MAAA,QAAQ,KAAK,KAAK,GAAG;AACvB,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO;AAAA,MACnC,QAAQ;AAAA,IAAA,CACT;AACD,QAAI,WAAW,OAAO;AACpB,aAAO,CAAC,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK;AAAA,IAAA;AAE5D,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EAAA;AAExB,MAAI,GAAG,GAAG;AACV,MAAI,WAAW,SAAS;AAClB,QAAA;AACA,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,eAAe,OAAO,GAAG;AAAA,IAAA,OAC1B;AACC,YAAA,gBAAgB,OAAO,GAAG;AAAA,IAAA;AAElC,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACZ,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AAAA,EAAA,OACpC;AACL,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAAA,EAAA;AAE3C,GAAA,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC7C,MAAI,WAAW,OAAO;AACpB,WAAO,CAAC,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK;AAAA,EAAA;AAErD,SAAA,CAAC,WAAW,WAAW,IAAI,KAAK,MAAM,OAAO,GAAG,GAAG,GAAG,KAAK;AACpE;AAQO,MAAM,oBAAoB,CAC/B,OACA,MAAe,OACgB;AAC3B,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,SAAS,GAAA,IAAO;AACpB,MAAA,GAAG,GAAG,GAAG;AACT,MAAA,QAAQ,KAAK,KAAK,GAAG;AACvB,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO;AAAA,MACnC,QAAQ;AAAA,IAAA,CACT;AACD,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EAAA;AAExB,MAAI,GAAG,GAAG;AACV,MAAI,WAAW,SAAS;AAClB,QAAA;AACJ,QAAI,MAAM;AACN,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,eAAe,OAAO,GAAG;AAAA,IAAA,OAC1B;AACC,YAAA,gBAAgB,OAAO,GAAG;AAAA,IAAA;AAElC,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACZ,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,OAAO;AAAA,MACzC,KAAK;AAAA,IAAA,CACN;AAAA,EAAA,OACI;AACL,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,OAAO;AAAA,MAC1C,KAAK;AAAA,IAAA,CACN;AAAA,EAAA;AAEF,GAAA,GAAG,GAAG,CAAC,IAAI,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAChD,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAQO,MAAM,oBAAoB,CAC/B,OACA,MAAe,OAC4D;AACvE,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,SAAS,GAAA,IAAO;AACpB,MAAA,GAAG,GAAG,GAAG;AACT,MAAA,QAAQ,KAAK,KAAK,GAAG;AACvB,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO;AAAA,MACnC,QAAQ;AAAA,IAAA,CACT;AACD,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EAAA;AAExB,MAAI,GAAG,GAAG;AACV,MAAI,WAAW,SAAS;AAClB,QAAA;AACJ,QAAI,MAAM;AACN,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,eAAe,OAAO,GAAG;AAAA,IAAA,OAC1B;AACC,YAAA,gBAAgB,OAAO,GAAG;AAAA,IAAA;AAElC,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACZ,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,OAAO;AAAA,MACzC,KAAK;AAAA,IAAA,CACN;AAAA,EAAA,OACI;AACL,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,OAAO;AAAA,MAC1C,KAAK;AAAA,IAAA,CACN;AAAA,EAAA;AAEF,GAAA,GAAG,GAAG,CAAC,IAAI,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AACzC,SAAA,CAAC,GAAG,GAAG,WAAW,WAAW,MAAM,IAAI,OAAO,GAAG,KAAK;AAC/D;AAQO,MAAM,sBAAsB,CACjC,OACA,MAAe,OACgB;AAC3B,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,SAAS,GAAA,IAAO;AACpB,MAAA,GAAG,GAAG,GAAG;AACT,MAAA,UAAU,KAAK,KAAK,GAAG;AACzB,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,OAAO;AAAA,MACrC,QAAQ;AAAA,IAAA,CACT;AACD,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EAAA;AAExB,MAAI,GAAG,GAAG;AACV,MAAI,WAAW,SAAS;AAClB,QAAA;AACA,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,eAAe,OAAO,GAAG;AAAA,IAAA,OAC1B;AACC,YAAA,gBAAgB,OAAO,GAAG;AAAA,IAAA;AAElC,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACZ,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AAAA,EAAA,OACpC;AACL,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAAA,EAAA;AAE3C,GAAA,GAAG,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC/C,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAQO,MAAM,sBAAsB,CACjC,OACA,MAAe,OAC4D;AACvE,MAAA,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EAAA,OACd;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,EAAE,SAAS,GAAA,IAAO;AACpB,MAAA,GAAG,GAAG,GAAG;AACT,MAAA,UAAU,KAAK,KAAK,GAAG;AACzB,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,OAAO;AAAA,MACrC,QAAQ;AAAA,IAAA,CACT;AACD,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EAAA;AAExB,MAAI,GAAG,GAAG;AACV,MAAI,WAAW,SAAS;AAClB,QAAA;AACA,QAAA,MAAM,WAAW,QAAQ,GAAG;AACxB,YAAA,eAAe,OAAO,GAAG;AAAA,IAAA,OAC1B;AACC,YAAA,gBAAgB,OAAO,GAAG;AAAA,IAAA;AAElC,QAAI,eAAe,YAAY;AACtB,aAAA;AAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACZ,WAAA,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AAAA,EAAA,OACpC;AACL,KAAG,EAAA,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAAA,EAAA;AAE3C,GAAA,GAAG,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AACxC,SAAA,CAAC,GAAG,GAAG,WAAW,WAAW,MAAM,IAAI,OAAO,GAAG,KAAK;AAC/D;AAQO,MAAM,kBAAkB,CAC7B,OACA,MAAe,OACkC;AAC7C,MAAA,SAAS,KAAK,GAAG;AACX,YAAA,MAAM,YAAY,EAAE,KAAK;AAAA,EAAA,OAC5B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,EAAE,SAAS,IAAI,WAAW,MAAU,IAAA;AAC1C,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AAChB,aAAA;AAAA,IAAA;AAET,UAAM,aAAa,aAAa;AAC5B,QAAA,SAAS,UAAU,GAAG;AACjB,aAAA;AAAA,IAAA;AAEF,WAAA;AAAA,EAAA;AAET,QAAM,cAAc,CAAC;AACrB,MAAI,CAAC,QAAQ,KAAK,KAAK,GAAG;AACxB,QAAI,MAAM,WAAW,MAAM,KAAK,aAAa,KAAK,KAAK,GAAG;AACxD,YAAM,gBAAgB,IAAI,OAAO,OAAO,MAAM,IAAI,MAAM,IAAI;AACtD,YAAA,QAAQ,MAAM,MAAM,YAAY;AACtC,iBAAW,QAAQ,OAAO;AACxB,YAAI,MAAM;AACJ,cAAA,MAAM,gBAAgB,MAAM;AAAA,YAC9B,QAAQ,WAAW,WAAW,SAAS;AAAA,UAAA,CACxC;AAEG,cAAA,MAAM,QAAQ,GAAG,GAAG;AACtB,kBAAM,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI;AAC7B,gBAAI,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,GAAG;AACxC,sBAAA;AACR;AAAA,YAAA;AAEE,gBAAA,cAAc,KAAK,EAAE,GAAG;AAC1B,kBAAI,OAAO,GAAG;AACZ,sBAAM,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,cAAA,OAC9B;AACC,sBAAA,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,cAAA;AAAA,YAC7C,WACS,OAAO,GAAG;AACnB,oBAAM,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,YAAA,OACxB;AACC,oBAAA,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,YAAA;AAAA,UAE9B,WAAA,CAAC,QAAQ,KAAK,GAAG,GAAG;AACrB,oBAAA;AACR;AAAA,UAAA;AAEF,sBAAY,KAAK,GAAG;AACZ,kBAAA,MAAM,QAAQ,MAAM,GAAG;AAAA,QAAA;AAAA,MACjC;AAEF,UAAI,CAAC,OAAO;AACV,cAAMF,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AACtDA,eAAAA;AAAAA,MAAA;AAAA,IACT,OACK;AACL,YAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AACtDA,aAAAA;AAAAA,IAAA;AAAA,EACT;AAEF,MAAI,aAAa;AACjB,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI,OAAO;AACX,MAAI,SAAS;AACb,MAAI,OAAO;AACP,MAAA,YAAY,UAAU,WAAW,UAAU;AAC7C,UAAM,gBAAgB,IAAI,OAAO,2BAA2B,MAAM,QAAQ;AAC1E,UAAM,CAAG,EAAA,EAAE,IAAI,MAAM,MAAM,aAAa;AACpC,QAAA,WAAW,KAAK,EAAE,GAAG;AACvB,OAAA,EAAG,YAAY,MAAM,IAAI,GAAG,MAAM,UAAU;AAAA,IAAA,OACvC;AACQ,mBAAA;AAAA,IAAA;AAEX,QAAA,YAAY,WAAW,GAAG;AACxB,UAAA,CAAC,OAAO,KAAK,IAAI;AACb,cAAA,MAAM,QAAQ,aAAa,IAAI;AAC/B,cAAA,MAAM,QAAQ,aAAa,IAAI;AACvC,YAAM,OAAO,IAAI,OAAO,IAAI,KAAK,YAAY,GAAG,KAAK;AACrD,YAAM,OAAO,IAAI,OAAO,IAAI,KAAK,YAAY,GAAG,KAAK;AACrD,OAAA,EAAG,QAAQ,IAAI,IAAI,MAAM,MAAM,IAAI;AACnC,OAAA,EAAG,QAAQ,IAAI,IAAI,MAAM,MAAM,IAAI;AAAA,IAAA,OAC9B;AACD,UAAA,CAAC,IAAI,IAAI;AACN,aAAA,KAAK,QAAQ,aAAa,IAAI;AACrC,YAAM,WAAW,GAAG,IAAI,UAAU,GAAG;AACrC,YAAM,eAAe,IAAI,IAAI,YAAY,GAAG;AAC5C,YAAM,cAAc,IAAI,OAAO,IAAI,YAAY,GAAG;AAClD,YAAM,cAAc,IAAI,OAAO,GAAG,YAAY,UAAU;AACxD,YAAM,eAAe,IAAI,OAAO,KAAK,cAAc,YAAY,GAAG,MAAM;AAEpE,UAAA,YAAY,KAAK,KAAK,GAAG;AAC3B,cAAM,MAAM,IAAI;AAAA,UACd,IAAI,YAAY,cAAc,QAAQ;AAAA,QACxC;AACA,cAAM,CAAA,EAAG,YAAY,UAAU,IAAI,MAAM,MAAM,GAAG;AAClD,SAAA,EAAG,QAAQ,IAAI,IAAI,WAAW,MAAM,YAAY;AAChD,SAAA,EAAG,QAAQ,IAAI,IAAI,WAAW,MAAM,WAAW;AAAA,MAAA,OAC1C;AACL,cAAM,MAAM,IAAI;AAAA,UACd,IAAI,QAAQ,cAAc,YAAY;AAAA,QACxC;AACA,cAAM,CAAA,EAAG,YAAY,UAAU,IAAI,MAAM,MAAM,GAAG;AAClD,SAAA,EAAG,QAAQ,IAAI,IAAI,WAAW,MAAM,WAAW;AAC/C,SAAA,EAAG,QAAQ,IAAI,IAAI,WAAW,MAAM,YAAY;AAAA,MAAA;AAAA,IAClD;AAAA,EACF,OACK;AACL,UAAM,CAAG,EAAA,IAAI,YAAY,UAAU,IAAI,MAAM;AAAA,MAC3C;AAAA,IACF;AACA,UAAM,MAAM,IAAI,OAAO,KAAK,cAAc,YAAY,GAAG,MAAM;AAC/D,KAAA,EAAG,QAAQ,IAAI,IAAI,WAAW,MAAM,GAAG;AACvC,KAAA,EAAG,QAAQ,IAAI,IAAI,WAAW,MAAM,GAAG;AACnC,QAAA,WAAW,KAAK,EAAE,GAAG;AACvB,OAAA,EAAG,YAAY,MAAM,IAAI,GAAG,MAAM,UAAU;AAAA,IAAA,OACvC;AACQ,mBAAA;AAAA,IAAA;AAAA,EACf;AAGF,MAAI,IAAI,IAAI;AACZ,MAAI,QAAQ,MAAM;AACV,UAAA,KAAK,WAAW,IAAI,IAAI;AACxB,UAAA,KAAK,WAAW,IAAI,IAAI;AAC9B,QAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AACxC,YAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AACtDA,aAAAA;AAAAA,IAAA;AAET,UAAM,SAAS,KAAK;AACpB,QAAI,WAAW,GAAG;AAChB,YAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AACtDA,aAAAA;AAAAA,IAAA;AAET,SAAK,KAAK;AACV,SAAK,KAAK;AACN,QAAA,SAAS,IAAI,SAAS;AAAA,EAAA,OACrB;AACL,QAAI,MAAM;AACH,WAAA,WAAW,IAAI,IAAI;AACpB,UAAA,KAAK,KAAK,KAAK,GAAG;AACpB,cAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AACtDA,eAAAA;AAAAA,MAAA;AAET,WAAK,IAAI;AAAA,eACA,MAAM;AACV,WAAA,WAAW,IAAI,IAAI;AACpB,UAAA,KAAK,KAAK,KAAK,GAAG;AACpB,cAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AACtDA,eAAAA;AAAAA,MAAA;AAET,WAAK,IAAI;AAAA,IAAA,OACJ;AACA,WAAA;AACA,WAAA;AAAA,IAAA;AAEH,QAAA;AAAA,EAAA;AAEN,MAAI,eAAe,OAAO;AACX,iBAAA;AAAA,EAAA;AAGf,MAAI,WAAW,UAAU;AACvB,QAAI,SAAS;AACb,QAAI,SAAS;AACT,QAAA,OAAO,WAAW,MAAM,GAAG;AACpB,eAAA;AAAA,IACA,WAAA,OAAO,WAAW,QAAQ,GAAG;AACtC,YAAM,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI;AAAA,QAC3B;AAAA,QACA;AAAA,MACF;AACA,UAAI,OAAO,GAAG;AACZ,iBAAS,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,MAAA,OACjC;AACI,iBAAA,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,MAAA;AAAA,IAChD,OACK;AACC,YAAA,MAAM,gBAAgB,QAAQ,GAAG;AACnC,UAAA,MAAM,QAAQ,GAAG,GAAG;AACtB,cAAM,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI;AAC7B,YAAI,OAAO,GAAG;AACZ,cAAI,OAAO,OAAO;AAChB,qBAAS,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,UAAA,OAC7B;AACL,qBAAS,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,UAAA;AAAA,QAClC,WACS,OAAO,OAAO;AACd,mBAAA,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,QAAA,OACrC;AACI,mBAAA,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,QAAA;AAAA,MAC1C,OACK;AACL,YAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK;AAC1B,mBAAS,UAAU,EAAE;AACd,iBAAA;AAAA,QAAA;AAEA,iBAAA;AAAA,MAAA;AAAA,IACX;AAEE,QAAA,OAAO,WAAW,MAAM,GAAG;AACpB,eAAA;AAAA,IACA,WAAA,OAAO,WAAW,QAAQ,GAAG;AACtC,YAAM,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI;AAAA,QAC3B;AAAA,QACA;AAAA,MACF;AACA,UAAI,OAAO,GAAG;AACZ,iBAAS,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,MAAA,OACjC;AACI,iBAAA,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,MAAA;AAAA,IAChD,OACK;AACC,YAAA,MAAM,gBAAgB,QAAQ,GAAG;AACnC,UAAA,MAAM,QAAQ,GAAG,GAAG;AACtB,cAAM,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI;AAC7B,YAAI,OAAO,GAAG;AACZ,cAAI,OAAO,OAAO;AAChB,qBAAS,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,UAAA,OAC7B;AACL,qBAAS,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,UAAA;AAAA,QAClC,WACS,OAAO,OAAO;AACd,mBAAA,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,QAAA,OACrC;AACI,mBAAA,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,QAAA;AAAA,MAC1C,OACK;AACL,YAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK;AAC1B,mBAAS,UAAU,EAAE;AACd,iBAAA;AAAA,QAAA;AAEA,iBAAA;AAAA,MAAA;AAAA,IACX;AAEF,QAAI,QAAQ,MAAM;AACN,gBAAA,IAAI,WAAW,IAAI,CAAC;AACpB,gBAAA,IAAI,WAAW,IAAI,CAAC;AAAA,eACrB,MAAM;AACTG,YAAAA,MAAK,WAAW,IAAI;AACtBA,UAAAA,QAAO,UAAU,MAAM;AACzB,kBAAU,IAAIA,GAAE;AAAA,MAAA;AAAA,eAET,MAAM;AACTA,YAAAA,MAAK,UAAU,WAAW,IAAI;AAChCA,UAAAA,QAAO,UAAU,MAAM;AACzB,kBAAU,IAAIA,GAAE;AAAA,MAAA;AAAA,IAClB;AAEF,QAAI,QAAQ;AACJH,YAAAA,OAAM,gBAAgB,UAAU,IAAI,MAAM,SAAS,MAAM,KAAK,MAAM;AAC1E,eAAS,UAAUA,IAAG;AACfA,aAAAA;AAAAA,IAAA,OACF;AACL,YAAMA,OAAM,gBAAgB,UAAU,KAAK,MAAM,KAAK,MAAM;AAC5D,eAAS,UAAUA,IAAG;AACfA,aAAAA;AAAAA,IAAA;AAAA,EACT;AAEF,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,QAAQ;AAER,MAAA,qBAAqB,KAAK,UAAU,GAAG;AACzC,QAAI,MAAM;AACV,QAAI,eAAe,QAAQ;AACrB,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAEC,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAAA,IACH,OACK;AACD,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,wBAAwB,QAAQ;AAAA,UACrC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAEC,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,wBAAwB,QAAQ;AAAA,UACrC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAAA,IACH;AAEE,QAAA,gBAAgB,cAAc,gBAAgB,YAAY;AAC5D,YAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AACtDA,aAAAA;AAAAA,IAAA;AAET,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAC7B,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AACvB,UAAA,QAAQ,QAAQ,QAAQ,QAAQ;AAChC,UAAA,QAAQ,QAAQ,QAAQ,QAAQ;AAChC,UAAA,QAAQ,QAAQ,QAAQ,QAAQ;AAChC,UAAA,YAAY,QAAQ,QAAQ,QAAQ;AAC1C,UAAM,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,IAC/C;AAAA,MACE,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB;AAAA,IACF;AACF,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AACzB,YAAQ,UAAU;AAClB,QAAI,UAAU,GAAG;AACX,UAAA,KAAK,KAAK,KAAK;AACf,UAAA,KAAK,KAAK,KAAK;AACf,UAAA,KAAK,KAAK,KAAK;AAAA,IAAA,OACd;AACA,WAAA,KAAK,UAAU,KAAK,WAAW;AAC/B,WAAA,KAAK,UAAU,KAAK,WAAW;AAC/B,WAAA,KAAK,UAAU,KAAK,WAAW;AACpC,cAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IAAA;AAErC,QAAI,WAAW,UAAU;AACvB,YAAMA,OAA8B;AAAA,QAClC;AAAA,QACA,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,YAAY,OAAO,QAAQ;AAAA,MAC7B;AACA,eAAS,UAAUA,IAAG;AACfA,aAAAA;AAAAA,IAAA;AAEJ,SAAA;AACA,SAAA;AACA,SAAA;AAAA,EAEI,WAAA,WAAW,KAAK,UAAU,GAAG;AACtC,QAAI,MAAM;AACN,QAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,aAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,IAAA,OACzB;AACL,aAAO,kBAAkB,QAAQ;AAAA,QAC/B;AAAA,QACA,KAAK,eAAe;AAAA,QACpB,QAAQ;AAAA,MAAA,CACT;AAAA,IAAA;AAEC,QAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,aAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,IAAA,OACzB;AACL,aAAO,kBAAkB,QAAQ;AAAA,QAC/B;AAAA,QACA,KAAK,eAAe;AAAA,QACpB,QAAQ;AAAA,MAAA,CACT;AAAA,IAAA;AAEC,QAAA,gBAAgB,cAAc,gBAAgB,YAAY;AAC5D,YAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AACtDA,aAAAA;AAAAA,IAAA;AAET,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAC7B,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AACvB,UAAA,QAAQ,QAAQ,QAAQ,QAAQ;AAChC,UAAA,QAAQ,QAAQ,QAAQ,QAAQ;AAChC,UAAA,QAAQ,QAAQ,QAAQ,QAAQ;AAChC,UAAA,YAAY,QAAQ,QAAQ,QAAQ;AAC1C,UAAM,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,IAC/C;AAAA,MACE,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB;AAAA,IACF;AACF,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AACzB,YAAQ,UAAU;AAClB,QAAI,GAAG,GAAG;AACV,QAAI,UAAU,GAAG;AACX,UAAA,KAAK,KAAK,KAAK;AACf,UAAA,KAAK,KAAK,KAAK;AACf,UAAA,KAAK,KAAK,KAAK;AAAA,IAAA,OACd;AACA,WAAA,KAAK,UAAU,KAAK,WAAW;AAC/B,WAAA,KAAK,UAAU,KAAK,WAAW;AAC/B,WAAA,KAAK,UAAU,KAAK,WAAW;AACpC,cAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IAAA;AAErC,QAAI,WAAW,UAAU;AACvB,YAAMA,OAA8B;AAAA,QAClC;AAAA,QACA,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,YAAY,OAAO,QAAQ;AAAA,MAC7B;AACA,eAAS,UAAUA,IAAG;AACfA,aAAAA;AAAAA,IAAA;AAET,QAAI,eAAe,WAAW;AAC3B,OAAA,GAAG,GAAG,CAAC,IAAI,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA,OAC3C;AACJ,OAAA,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAAA,EAGtC,WAAA,eAAe,KAAK,UAAU,GAAG;AAC1C,QAAI,MAAM;AACV,QAAI,eAAe,OAAO;AACpB,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAEC,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAAA,IACH,OACK;AACD,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAEC,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAAA,IACH;AAEE,QAAA,gBAAgB,cAAc,gBAAgB,YAAY;AAC5D,YAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AACtDA,aAAAA;AAAAA,IAAA;AAET,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAC7B,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AACvB,UAAA,YAAY,QAAQ,QAAQ,QAAQ;AAC1C,QAAI,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI;AAAA,MACjD,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB;AAAA,IACF;AACA,QAAI,QAAQ;AACV,OAAC,IAAI,EAAE,IAAI,eAAe,IAAI,IAAI,MAAM;AAAA,IAAA;AAE1C,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AACzB,YAAQ,UAAU;AAClB,UAAM,KAAK,KAAK,KAAK,KAAK,MAAM;AAChC,QAAI,GAAG;AACP,QAAI,UAAU,GAAG;AACX,UAAA,KAAK,KAAK,KAAK;AACf,UAAA,KAAK,KAAK,KAAK;AAAA,IAAA,OACd;AACA,WAAA,KAAK,UAAU,KAAK,WAAW;AAC/B,WAAA,KAAK,UAAU,KAAK,WAAW;AACpC,cAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IAAA;AAEpC,KAAA,GAAG,GAAG,CAAC,IAAI;AAAA,MACV,GAAG,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,IAC9B;AACA,QAAI,WAAW,UAAU;AACvB,YAAMA,OAA8B;AAAA,QAClC;AAAA,QACA,iBAAiB,IAAI,SAAS,GAAG;AAAA,QACjC,iBAAiB,IAAI,SAAS,GAAG;AAAA,QACjC,iBAAiB,IAAI,SAAS,GAAG;AAAA,QACjC,YAAY,OAAO,QAAQ;AAAA,MAC7B;AACA,eAAS,UAAUA,IAAG;AACfA,aAAAA;AAAAA,IAAA;AAAA,EAGA,WAAA,eAAe,KAAK,UAAU,GAAG;AAC1C,QAAI,MAAM;AACV,QAAI,eAAe,OAAO;AACpB,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAEC,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAAA,IACH,OACK;AACD,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,oBAAoB,QAAQ;AAAA,UACjC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAEC,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,oBAAoB,QAAQ;AAAA,UACjC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAAA,IACH;AAEE,QAAA,gBAAgB,cAAc,gBAAgB,YAAY;AAC5D,YAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AACtDA,aAAAA;AAAAA,IAAA;AAET,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAC7B,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AACvB,UAAA,QAAQ,QAAQ,QAAQ,QAAQ;AAChC,UAAA,QAAQ,QAAQ,QAAQ,QAAQ;AAChC,UAAA,QAAQ,QAAQ,QAAQ,QAAQ;AAChC,UAAA,YAAY,QAAQ,QAAQ,QAAQ;AAC1C,QAAI,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI;AAAA,MACjD,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB;AAAA,IACF;AACA,QAAI,QAAQ;AACV,OAAC,IAAI,EAAE,IAAI,eAAe,IAAI,IAAI,MAAM;AAAA,IAAA;AAE1C,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AACzB,YAAQ,UAAU;AAClB,UAAM,KAAK,KAAK,KAAK,KAAK,MAAM;AAChC,QAAI,GAAG;AACP,QAAI,UAAU,GAAG;AACX,UAAA,KAAK,KAAK,KAAK;AACf,UAAA,KAAK,KAAK,KAAK;AAAA,IAAA,OACd;AACA,WAAA,KAAK,UAAU,KAAK,WAAW;AAC/B,WAAA,KAAK,UAAU,KAAK,WAAW;AACpC,cAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IAAA;AAErC,QAAI,WAAW,UAAU;AACvB,YAAMA,OAA8B;AAAA,QAClC;AAAA,QACA,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,YAAY,OAAO,QAAQ;AAAA,MAC7B;AACA,eAAS,UAAUA,IAAG;AACfA,aAAAA;AAAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,CAAC,IAAI;AAAA,MACZ,GAAG,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,IAC9B;AAAA,EAAA,OAEK;AACL,QAAI,MAAM;AACV,QAAI,eAAe,OAAO;AACpB,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAEC,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAAA,IACH,OACK;AACD,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,oBAAoB,QAAQ;AAAA,UACjC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAEC,UAAA,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAAA,OACzB;AACL,eAAO,oBAAoB,QAAQ;AAAA,UACjC;AAAA,UACA,QAAQ;AAAA,QAAA,CACT;AAAA,MAAA;AAAA,IACH;AAEE,QAAA,gBAAgB,cAAc,gBAAgB,YAAY;AAC5D,YAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AACtDA,aAAAA;AAAAA,IAAA;AAET,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAC7B,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AACvB,UAAA,QAAQ,QAAQ,QAAQ,QAAQ;AAChC,UAAA,QAAQ,QAAQ,QAAQ,QAAQ;AAChC,UAAA,QAAQ,QAAQ,QAAQ,QAAQ;AAChC,UAAA,YAAY,QAAQ,QAAQ,QAAQ;AAC1C,UAAM,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,IAC/C;AAAA,MACE,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB;AAAA,IACF;AACF,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AACzB,YAAQ,UAAU;AAClB,QAAI,GAAG,IAAI;AACX,QAAI,UAAU,GAAG;AACX,UAAA,KAAK,KAAK,KAAK;AACd,WAAA,KAAK,KAAK,KAAK;AACf,WAAA,KAAK,KAAK,KAAK;AAAA,IAAA,OACf;AACA,WAAA,KAAK,UAAU,KAAK,WAAW;AAC9B,YAAA,KAAK,UAAU,KAAK,WAAW;AAC/B,YAAA,KAAK,UAAU,KAAK,WAAW;AACrC,cAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IAAA;AAErC,QAAI,WAAW,UAAU;AACvB,YAAMA,OAA8B;AAAA,QAClC;AAAA,QACA,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,IAAI,GAAG;AAAA,QACvC,QAAQ,OAAO,iBAAiB,IAAI,GAAG;AAAA,QACvC,YAAY,OAAO,QAAQ;AAAA,MAC7B;AACA,eAAS,UAAUA,IAAG;AACfA,aAAAA;AAAAA,IAAA;AAET,KAAA,EAAG,GAAG,GAAG,CAAC,IAAI;AAAA,MACZ,GAAG,UAAU,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;AAAA,IAChC;AAAA,EAAA;AAEF,QAAM,MAA8B;AAAA,IAClC;AAAA,IACA,KAAK,MAAM,CAAC;AAAA,IACZ,KAAK,MAAM,CAAC;AAAA,IACZ,KAAK,MAAM,CAAC;AAAA,IACZ,YAAY,QAAQ,GAAG,QAAQ,CAAC,CAAC;AAAA,EACnC;AACA,WAAS,UAAU,GAAG;AACf,SAAA;AACT;"}