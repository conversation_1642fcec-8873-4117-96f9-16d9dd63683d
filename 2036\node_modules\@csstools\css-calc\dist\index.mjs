import{TokenNode as e,isTokenNode as n,isWhitespaceNode as t,isCommentNode as r,isWhiteSpaceOrCommentNode as a,isSimpleBlockNode as u,isFunctionNode as i,FunctionNode as o,WhitespaceNode as l,parseCommaSeparatedListOfComponentValues as c,replaceComponentValues as s}from"@csstools/css-parser-algorithms";import{isTokenDimension as v,TokenType as f,NumberType as m,mutateUnit as p,isTokenNumber as C,isTokenPercentage as g,isTokenIdent as d,isTokenNumeric as D,isTokenOpenParen as N,isTokenDelim as B,isTokenComma as A,isToken as h,tokenizer as F,tokenize as b,stringify as w}from"@csstools/css-tokenizer";const E=/[A-Z]/g;function toLowerCaseAZ(e){return e.replace(E,(e=>String.fromCharCode(e.charCodeAt(0)+32)))}const I={cm:"px",in:"px",mm:"px",pc:"px",pt:"px",px:"px",q:"px",deg:"deg",grad:"deg",rad:"deg",turn:"deg",ms:"s",s:"s",hz:"hz",khz:"hz"},S=new Map([["cm",e=>e],["mm",e=>10*e],["q",e=>40*e],["in",e=>e/2.54],["pc",e=>e/2.54*6],["pt",e=>e/2.54*72],["px",e=>e/2.54*96]]),y=new Map([["deg",e=>e],["grad",e=>e/.9],["rad",e=>e/180*Math.PI],["turn",e=>e/360]]),M=new Map([["deg",e=>.9*e],["grad",e=>e],["rad",e=>.9*e/180*Math.PI],["turn",e=>.9*e/360]]),T=new Map([["hz",e=>e],["khz",e=>e/1e3]]),x=new Map([["cm",e=>2.54*e],["mm",e=>25.4*e],["q",e=>25.4*e*4],["in",e=>e],["pc",e=>6*e],["pt",e=>72*e],["px",e=>96*e]]),k=new Map([["hz",e=>1e3*e],["khz",e=>e]]),P=new Map([["cm",e=>e/10],["mm",e=>e],["q",e=>4*e],["in",e=>e/25.4],["pc",e=>e/25.4*6],["pt",e=>e/25.4*72],["px",e=>e/25.4*96]]),O=new Map([["ms",e=>e],["s",e=>e/1e3]]),W=new Map([["cm",e=>e/6*2.54],["mm",e=>e/6*25.4],["q",e=>e/6*25.4*4],["in",e=>e/6],["pc",e=>e],["pt",e=>e/6*72],["px",e=>e/6*96]]),L=new Map([["cm",e=>e/72*2.54],["mm",e=>e/72*25.4],["q",e=>e/72*25.4*4],["in",e=>e/72],["pc",e=>e/72*6],["pt",e=>e],["px",e=>e/72*96]]),U=new Map([["cm",e=>e/96*2.54],["mm",e=>e/96*25.4],["q",e=>e/96*25.4*4],["in",e=>e/96],["pc",e=>e/96*6],["pt",e=>e/96*72],["px",e=>e]]),$=new Map([["cm",e=>e/4/10],["mm",e=>e/4],["q",e=>e],["in",e=>e/4/25.4],["pc",e=>e/4/25.4*6],["pt",e=>e/4/25.4*72],["px",e=>e/4/25.4*96]]),Z=new Map([["deg",e=>180*e/Math.PI],["grad",e=>180*e/Math.PI/.9],["rad",e=>e],["turn",e=>180*e/Math.PI/360]]),z=new Map([["ms",e=>1e3*e],["s",e=>e]]),q=new Map([["deg",e=>360*e],["grad",e=>360*e/.9],["rad",e=>360*e/180*Math.PI],["turn",e=>e]]),G=new Map([["cm",S],["mm",P],["q",$],["in",x],["pc",W],["pt",L],["px",U],["ms",O],["s",z],["deg",y],["grad",M],["rad",Z],["turn",q],["hz",T],["khz",k]]);function convertUnit(e,n){if(!v(e))return n;if(!v(n))return n;const t=toLowerCaseAZ(e[4].unit),r=toLowerCaseAZ(n[4].unit);if(t===r)return n;const a=G.get(r);if(!a)return n;const u=a.get(t);if(!u)return n;const i=u(n[4].value),o=[f.Dimension,"",n[2],n[3],{...n[4],signCharacter:i<0?"-":void 0,type:Number.isInteger(i)?m.Integer:m.Number,value:i}];return p(o,e[4].unit),o}function toCanonicalUnit(e){if(!v(e))return e;const n=toLowerCaseAZ(e[4].unit),t=I[n];if(n===t)return e;const r=G.get(n);if(!r)return e;const a=r.get(t);if(!a)return e;const u=a(e[4].value),i=[f.Dimension,"",e[2],e[3],{...e[4],signCharacter:u<0?"-":void 0,type:Number.isInteger(u)?m.Integer:m.Number,value:u}];return p(i,t),i}function addition(n){if(2!==n.length)return-1;const t=n[0].value;let r=n[1].value;if(C(t)&&C(r)){const n=t[4].value+r[4].value;return new e([f.Number,n.toString(),t[2],r[3],{value:n,type:t[4].type===m.Integer&&r[4].type===m.Integer?m.Integer:m.Number}])}if(g(t)&&g(r)){const n=t[4].value+r[4].value;return new e([f.Percentage,n.toString()+"%",t[2],r[3],{value:n}])}if(v(t)&&v(r)&&(r=convertUnit(t,r),toLowerCaseAZ(t[4].unit)===toLowerCaseAZ(r[4].unit))){const n=t[4].value+r[4].value;return new e([f.Dimension,n.toString()+t[4].unit,t[2],r[3],{value:n,type:t[4].type===m.Integer&&r[4].type===m.Integer?m.Integer:m.Number,unit:t[4].unit}])}return-1}function division(n){if(2!==n.length)return-1;const t=n[0].value,r=n[1].value;if(C(t)&&C(r)){const n=t[4].value/r[4].value;return new e([f.Number,n.toString(),t[2],r[3],{value:n,type:Number.isInteger(n)?m.Integer:m.Number}])}if(g(t)&&C(r)){const n=t[4].value/r[4].value;return new e([f.Percentage,n.toString()+"%",t[2],r[3],{value:n}])}if(v(t)&&C(r)){const n=t[4].value/r[4].value;return new e([f.Dimension,n.toString()+t[4].unit,t[2],r[3],{value:n,type:Number.isInteger(n)?m.Integer:m.Number,unit:t[4].unit}])}return-1}function isCalculation(e){return!!e&&"object"==typeof e&&"inputs"in e&&Array.isArray(e.inputs)&&"operation"in e}function solve(e){if(-1===e)return-1;const t=[];for(let r=0;r<e.inputs.length;r++){const a=e.inputs[r];if(n(a)){t.push(a);continue}const u=solve(a);if(-1===u)return-1;t.push(u)}return e.operation(t)}function multiplication(n){if(2!==n.length)return-1;const t=n[0].value,r=n[1].value;if(C(t)&&C(r)){const n=t[4].value*r[4].value;return new e([f.Number,n.toString(),t[2],r[3],{value:n,type:t[4].type===m.Integer&&r[4].type===m.Integer?m.Integer:m.Number}])}if(g(t)&&C(r)){const n=t[4].value*r[4].value;return new e([f.Percentage,n.toString()+"%",t[2],r[3],{value:n}])}if(C(t)&&g(r)){const n=t[4].value*r[4].value;return new e([f.Percentage,n.toString()+"%",t[2],r[3],{value:n}])}if(v(t)&&C(r)){const n=t[4].value*r[4].value;return new e([f.Dimension,n.toString()+t[4].unit,t[2],r[3],{value:n,type:t[4].type===m.Integer&&r[4].type===m.Integer?m.Integer:m.Number,unit:t[4].unit}])}if(C(t)&&v(r)){const n=t[4].value*r[4].value;return new e([f.Dimension,n.toString()+r[4].unit,t[2],r[3],{value:n,type:t[4].type===m.Integer&&r[4].type===m.Integer?m.Integer:m.Number,unit:r[4].unit}])}return-1}function resolveGlobalsAndConstants(t,r){for(let a=0;a<t.length;a++){const u=t[a];if(!n(u))continue;const i=u.value;if(!d(i))continue;const o=toLowerCaseAZ(i[4].value);switch(o){case"e":t.splice(a,1,new e([f.Number,Math.E.toString(),i[2],i[3],{value:Math.E,type:m.Number}]));break;case"pi":t.splice(a,1,new e([f.Number,Math.PI.toString(),i[2],i[3],{value:Math.PI,type:m.Number}]));break;case"infinity":t.splice(a,1,new e([f.Number,"infinity",i[2],i[3],{value:1/0,type:m.Number}]));break;case"-infinity":t.splice(a,1,new e([f.Number,"-infinity",i[2],i[3],{value:-1/0,type:m.Number}]));break;case"nan":t.splice(a,1,new e([f.Number,"NaN",i[2],i[3],{value:Number.NaN,type:m.Number}]));break;default:if(r.has(o)){const n=r.get(o);t.splice(a,1,new e(n))}}}return t}function unary(e){if(1!==e.length)return-1;const n=e[0].value;return D(n)?e[0]:-1}function resultToCalculation(e,n,t){return v(n)?dimensionToCalculation(e,n[4].unit,t):g(n)?percentageToCalculation(e,t):C(n)?numberToCalculation(e,t):-1}function dimensionToCalculation(n,t,r){const a=n.tokens();return{inputs:[new e([f.Dimension,r.toString()+t,a[0][2],a[a.length-1][3],{value:r,type:Number.isInteger(r)?m.Integer:m.Number,unit:t}])],operation:unary}}function percentageToCalculation(n,t){const r=n.tokens();return{inputs:[new e([f.Percentage,t.toString()+"%",r[0][2],r[r.length-1][3],{value:t}])],operation:unary}}function numberToCalculation(n,t){const r=n.tokens();return{inputs:[new e([f.Number,t.toString(),r[0][2],r[r.length-1][3],{value:t,type:Number.isInteger(t)?m.Integer:m.Number}])],operation:unary}}function solveACos(e,n){const t=n.value;if(!C(t))return-1;return dimensionToCalculation(e,"rad",Math.acos(t[4].value))}function solveASin(e,n){const t=n.value;if(!C(t))return-1;return dimensionToCalculation(e,"rad",Math.asin(t[4].value))}function solveATan(e,n){const t=n.value;if(!C(t))return-1;return dimensionToCalculation(e,"rad",Math.atan(t[4].value))}function isDimensionOrNumber(e){return v(e)||C(e)}function arrayOfSameNumeric(e){if(0===e.length)return!0;const n=e[0];if(!D(n))return!1;if(1===e.length)return!0;if(v(n)){const t=toLowerCaseAZ(n[4].unit);for(let r=1;r<e.length;r++){const a=e[r];if(n[0]!==a[0])return!1;if(t!==toLowerCaseAZ(a[4].unit))return!1}return!0}for(let t=1;t<e.length;t++){const r=e[t];if(n[0]!==r[0])return!1}return!0}function twoOfSameNumeric(e,n){return!!D(e)&&(v(e)?e[0]===n[0]&&toLowerCaseAZ(e[4].unit)===toLowerCaseAZ(n[4].unit):e[0]===n[0])}function solveATan2(e,n,t){const r=n.value;if(!isDimensionOrNumber(r))return-1;const a=convertUnit(r,t.value);if(!twoOfSameNumeric(r,a))return-1;return dimensionToCalculation(e,"rad",Math.atan2(r[4].value,a[4].value))}function solveAbs(e,n,t){const r=n.value;if(!D(r))return-1;if(!t.rawPercentages&&g(r))return-1;return resultToCalculation(e,r,Math.abs(r[4].value))}function solveClamp(e,t,r,a,u){if(!n(t)||!n(r)||!n(a))return-1;const i=t.value;if(!D(i))return-1;if(!u.rawPercentages&&g(i))return-1;const o=convertUnit(i,r.value);if(!twoOfSameNumeric(i,o))return-1;const l=convertUnit(i,a.value);if(!twoOfSameNumeric(i,l))return-1;return resultToCalculation(e,i,Math.max(i[4].value,Math.min(o[4].value,l[4].value)))}function solveCos(e,n){const t=n.value;if(!isDimensionOrNumber(t))return-1;let r=t[4].value;if(v(t))switch(t[4].unit.toLowerCase()){case"rad":break;case"deg":r=y.get("rad")(t[4].value);break;case"grad":r=M.get("rad")(t[4].value);break;case"turn":r=q.get("rad")(t[4].value);break;default:return-1}return r=Math.cos(r),numberToCalculation(e,r)}function solveExp(e,n){const t=n.value;if(!C(t))return-1;return numberToCalculation(e,Math.exp(t[4].value))}function solveHypot(e,t,r){if(!t.every(n))return-1;const a=t[0].value;if(!D(a))return-1;if(!r.rawPercentages&&g(a))return-1;const u=t.map((e=>convertUnit(a,e.value)));if(!arrayOfSameNumeric(u))return-1;const i=u.map((e=>e[4].value)),o=Math.hypot(...i);return resultToCalculation(e,a,o)}function solveMax(e,t,r){if(!t.every(n))return-1;const a=t[0].value;if(!D(a))return-1;if(!r.rawPercentages&&g(a))return-1;const u=t.map((e=>convertUnit(a,e.value)));if(!arrayOfSameNumeric(u))return-1;const i=u.map((e=>e[4].value)),o=Math.max(...i);return resultToCalculation(e,a,o)}function solveMin(e,t,r){if(!t.every(n))return-1;const a=t[0].value;if(!D(a))return-1;if(!r.rawPercentages&&g(a))return-1;const u=t.map((e=>convertUnit(a,e.value)));if(!arrayOfSameNumeric(u))return-1;const i=u.map((e=>e[4].value)),o=Math.min(...i);return resultToCalculation(e,a,o)}function solveMod(e,n,t){const r=n.value;if(!D(r))return-1;const a=convertUnit(r,t.value);if(!twoOfSameNumeric(r,a))return-1;let u;return u=0===a[4].value?Number.NaN:Number.isFinite(r[4].value)&&(Number.isFinite(a[4].value)||(a[4].value!==Number.POSITIVE_INFINITY||r[4].value!==Number.NEGATIVE_INFINITY&&!Object.is(0*r[4].value,-0))&&(a[4].value!==Number.NEGATIVE_INFINITY||r[4].value!==Number.POSITIVE_INFINITY&&!Object.is(0*r[4].value,0)))?Number.isFinite(a[4].value)?(r[4].value%a[4].value+a[4].value)%a[4].value:r[4].value:Number.NaN,resultToCalculation(e,r,u)}function solvePow(e,n,t){const r=n.value,a=t.value;if(!C(r))return-1;if(!twoOfSameNumeric(r,a))return-1;return numberToCalculation(e,Math.pow(r[4].value,a[4].value))}function solveRem(e,n,t){const r=n.value;if(!D(r))return-1;const a=convertUnit(r,t.value);if(!twoOfSameNumeric(r,a))return-1;let u;return u=0===a[4].value?Number.NaN:Number.isFinite(r[4].value)?Number.isFinite(a[4].value)?r[4].value%a[4].value:r[4].value:Number.NaN,resultToCalculation(e,r,u)}function solveRound(e,n,t,r,a){const u=t.value;if(!D(u))return-1;if(!a.rawPercentages&&g(u))return-1;const i=convertUnit(u,r.value);if(!twoOfSameNumeric(u,i))return-1;let o;if(0===i[4].value)o=Number.NaN;else if(Number.isFinite(u[4].value)||Number.isFinite(i[4].value))if(!Number.isFinite(u[4].value)&&Number.isFinite(i[4].value))o=u[4].value;else if(Number.isFinite(u[4].value)&&!Number.isFinite(i[4].value))switch(n){case"down":o=u[4].value<0?-1/0:Object.is(-0,0*u[4].value)?-0:0;break;case"up":o=u[4].value>0?1/0:Object.is(0,0*u[4].value)?0:-0;break;default:o=Object.is(0,0*u[4].value)?0:-0}else if(Number.isFinite(i[4].value))switch(n){case"down":o=Math.floor(u[4].value/i[4].value)*i[4].value;break;case"up":o=Math.ceil(u[4].value/i[4].value)*i[4].value;break;case"to-zero":o=Math.trunc(u[4].value/i[4].value)*i[4].value;break;default:{let e=Math.floor(u[4].value/i[4].value)*i[4].value,n=Math.ceil(u[4].value/i[4].value)*i[4].value;if(e>n){const t=e;e=n,n=t}const t=Math.abs(u[4].value-e),r=Math.abs(u[4].value-n);o=t===r?n:t<r?e:n;break}}else o=u[4].value;else o=Number.NaN;return resultToCalculation(e,u,o)}function solveSign(e,n,t){const r=n.value;if(!D(r))return-1;if(!t.rawPercentages&&g(r))return-1;return numberToCalculation(e,Math.sign(r[4].value))}function solveSin(e,n){const t=n.value;if(!isDimensionOrNumber(t))return-1;let r=t[4].value;if(v(t))switch(toLowerCaseAZ(t[4].unit)){case"rad":break;case"deg":r=y.get("rad")(t[4].value);break;case"grad":r=M.get("rad")(t[4].value);break;case"turn":r=q.get("rad")(t[4].value);break;default:return-1}return r=Math.sin(r),numberToCalculation(e,r)}function solveSqrt(e,n){const t=n.value;if(!C(t))return-1;return numberToCalculation(e,Math.sqrt(t[4].value))}function solveTan(e,n){const t=n.value;if(!isDimensionOrNumber(t))return-1;const r=t[4].value;let a=0,u=t[4].value;if(v(t))switch(toLowerCaseAZ(t[4].unit)){case"rad":a=Z.get("deg")(r);break;case"deg":a=r,u=y.get("rad")(r);break;case"grad":a=M.get("deg")(r),u=M.get("rad")(r);break;case"turn":a=q.get("deg")(r),u=q.get("rad")(r);break;default:return-1}const i=a/90;return u=a%90==0&&i%2!=0?i>0?1/0:-1/0:Math.tan(u),numberToCalculation(e,u)}function subtraction(n){if(2!==n.length)return-1;const t=n[0].value;let r=n[1].value;if(C(t)&&C(r)){const n=t[4].value-r[4].value;return new e([f.Number,n.toString(),t[2],r[3],{value:n,type:t[4].type===m.Integer&&r[4].type===m.Integer?m.Integer:m.Number}])}if(g(t)&&g(r)){const n=t[4].value-r[4].value;return new e([f.Percentage,n.toString()+"%",t[2],r[3],{value:n}])}if(v(t)&&v(r)&&(r=convertUnit(t,r),toLowerCaseAZ(t[4].unit)===toLowerCaseAZ(r[4].unit))){const n=t[4].value-r[4].value;return new e([f.Dimension,n.toString()+t[4].unit,t[2],r[3],{value:n,type:t[4].type===m.Integer&&r[4].type===m.Integer?m.Integer:m.Number,unit:t[4].unit}])}return-1}function solveLog(e,t){if(1===t.length){const r=t[0];if(!r||!n(r))return-1;const a=r.value;if(!C(a))return-1;return numberToCalculation(e,Math.log(a[4].value))}if(2===t.length){const r=t[0];if(!r||!n(r))return-1;const a=r.value;if(!C(a))return-1;const u=t[1];if(!u||!n(u))return-1;const i=u.value;if(!C(i))return-1;return numberToCalculation(e,Math.log(a[4].value)/Math.log(i[4].value))}return-1}const R=/^none$/i;function isNone(e){if(Array.isArray(e)){const n=e.filter((e=>!(t(e)&&r(e))));return 1===n.length&&isNone(n[0])}if(!n(e))return!1;const a=e.value;return!!d(a)&&R.test(a[4].value)}const V=String.fromCodePoint(0);function solveRandom(e,n,t,r,a,u){if(-1===n.fixed&&!u.randomCaching)return-1;u.randomCaching||(u.randomCaching={propertyName:"",propertyN:0,elementID:"",documentID:""}),u.randomCaching&&!u.randomCaching.propertyN&&(u.randomCaching.propertyN=0);const i=t.value;if(!D(i))return-1;const o=convertUnit(i,r.value);if(!twoOfSameNumeric(i,o))return-1;let l=null;if(a&&(l=convertUnit(i,a.value),!twoOfSameNumeric(i,l)))return-1;if(!Number.isFinite(i[4].value))return resultToCalculation(e,i,Number.NaN);if(!Number.isFinite(o[4].value))return resultToCalculation(e,i,Number.NaN);if(!Number.isFinite(o[4].value-i[4].value))return resultToCalculation(e,i,Number.NaN);if(l&&!Number.isFinite(l[4].value))return resultToCalculation(e,i,i[4].value);const c=-1===n.fixed?sfc32(crc32([n.dashedIdent?n.dashedIdent:`${u.randomCaching?.propertyName} ${u.randomCaching.propertyN++}`,n.elementShared?"":u.randomCaching.elementID,u.randomCaching.documentID].join(V))):()=>n.fixed;let s=i[4].value,v=o[4].value;if(s>v&&([s,v]=[v,s]),l&&(l[4].value<=0||Math.abs(s-v)/l[4].value>1e10)&&(l=null),l){const n=Math.max(l[4].value/1e3,1e-9),t=[s];let r=0;for(;;){r+=l[4].value;const e=s+r;if(!(e+n<v)){t.push(v);break}if(t.push(e),e+l[4].value-n>v)break}const a=c();return resultToCalculation(e,i,Number(t[Math.floor(t.length*a)].toFixed(5)))}const f=c();return resultToCalculation(e,i,Number((f*(v-s)+s).toFixed(5)))}function sfc32(e=.34944106645296036,n=.19228640875738723,t=.8784393832007205,r=.04850964319275053){return()=>{const a=((e|=0)+(n|=0)|0)+(r|=0)|0;return r=r+1|0,e=n^n>>>9,n=(t|=0)+(t<<3)|0,t=(t=t<<21|t>>>11)+a|0,(a>>>0)/4294967296}}function crc32(e){let n=0,t=0,r=0;n=~n;for(let a=0,u=e.length;a<u;a++)r=255&(n^e.charCodeAt(a)),t=Number("0x"+"00000000 77073096 EE0E612C 990951BA 076DC419 706AF48F E963A535 9E6495A3 0EDB8832 79DCB8A4 E0D5E91E 97D2D988 09B64C2B 7EB17CBD E7B82D07 90BF1D91 1DB71064 6AB020F2 F3B97148 84BE41DE 1ADAD47D 6DDDE4EB F4D4B551 83D385C7 136C9856 646BA8C0 FD62F97A 8A65C9EC 14015C4F 63066CD9 FA0F3D63 8D080DF5 3B6E20C8 4C69105E D56041E4 A2677172 3C03E4D1 4B04D447 D20D85FD A50AB56B 35B5A8FA 42B2986C DBBBC9D6 ACBCF940 32D86CE3 45DF5C75 DCD60DCF ABD13D59 26D930AC 51DE003A C8D75180 BFD06116 21B4F4B5 56B3C423 CFBA9599 B8BDA50F 2802B89E 5F058808 C60CD9B2 B10BE924 2F6F7C87 58684C11 C1611DAB B6662D3D 76DC4190 01DB7106 98D220BC EFD5102A 71B18589 06B6B51F 9FBFE4A5 E8B8D433 7807C9A2 0F00F934 9609A88E E10E9818 7F6A0DBB 086D3D2D 91646C97 E6635C01 6B6B51F4 1C6C6162 856530D8 F262004E 6C0695ED 1B01A57B 8208F4C1 F50FC457 65B0D9C6 12B7E950 8BBEB8EA FCB9887C 62DD1DDF 15DA2D49 8CD37CF3 FBD44C65 4DB26158 3AB551CE A3BC0074 D4BB30E2 4ADFA541 3DD895D7 A4D1C46D D3D6F4FB 4369E96A 346ED9FC AD678846 DA60B8D0 44042D73 33031DE5 AA0A4C5F DD0D7CC9 5005713C 270241AA BE0B1010 C90C2086 5768B525 206F85B3 B966D409 CE61E49F 5EDEF90E 29D9C998 B0D09822 C7D7A8B4 59B33D17 2EB40D81 B7BD5C3B C0BA6CAD EDB88320 9ABFB3B6 03B6E20C 74B1D29A EAD54739 9DD277AF 04DB2615 73DC1683 E3630B12 94643B84 0D6D6A3E 7A6A5AA8 E40ECF0B 9309FF9D 0A00AE27 7D079EB1 F00F9344 8708A3D2 1E01F268 6906C2FE F762575D 806567CB 196C3671 6E6B06E7 FED41B76 89D32BE0 10DA7A5A 67DD4ACC F9B9DF6F 8EBEEFF9 17B7BE43 60B08ED5 D6D6A3E8 A1D1937E 38D8C2C4 4FDFF252 D1BB67F1 A6BC5767 3FB506DD 48B2364B D80D2BDA AF0A1B4C 36034AF6 41047A60 DF60EFC3 A867DF55 316E8EEF 4669BE79 CB61B38C BC66831A 256FD2A0 5268E236 CC0C7795 BB0B4703 220216B9 5505262F C5BA3BBE B2BD0B28 2BB45A92 5CB36A04 C2D7FFA7 B5D0CF31 2CD99E8B 5BDEAE1D 9B64C2B0 EC63F226 756AA39C 026D930A 9C0906A9 EB0E363F 72076785 05005713 95BF4A82 E2B87A14 7BB12BAE 0CB61B38 92D28E9B E5D5BE0D 7CDCEFB7 0BDBDF21 86D3D2D4 F1D4E242 68DDB3F8 1FDA836E 81BE16CD F6B9265B 6FB077E1 18B74777 88085AE6 FF0F6A70 66063BCA 11010B5C 8F659EFF F862AE69 616BFFD3 166CCF45 A00AE278 D70DD2EE 4E048354 3903B3C2 A7672661 D06016F7 4969474D 3E6E77DB AED16A4A D9D65ADC 40DF0B66 37D83BF0 A9BCAE53 DEBB9EC5 47B2CF7F 30B5FFE9 BDBDF21C CABAC28A 53B39330 24B4A3A6 BAD03605 CDD70693 54DE5729 23D967BF B3667A2E C4614AB8 5D681B02 2A6F2B94 B40BBE37 C30C8EA1 5A05DF1B 2D02EF8D".substring(9*r,9*r+8)),n=n>>>8^t;return~n>>>0}const j=new Map([["abs",function abs(e,n,t){return singleNodeSolver(e,n,t,solveAbs)}],["acos",function acos(e,n,t){return singleNodeSolver(e,n,t,solveACos)}],["asin",function asin(e,n,t){return singleNodeSolver(e,n,t,solveASin)}],["atan",function atan(e,n,t){return singleNodeSolver(e,n,t,solveATan)}],["atan2",function atan2(e,n,t){return twoCommaSeparatedNodesSolver(e,n,t,solveATan2)}],["calc",calc$1],["clamp",function clamp(t,r,u){const i=resolveGlobalsAndConstants([...t.value.filter((e=>!a(e)))],r),l=[],c=[],s=[];{let e=l;for(let t=0;t<i.length;t++){const r=i[t];if(n(r)&&A(r.value)){if(e===s)return-1;if(e===c){e=s;continue}if(e===l){e=c;continue}return-1}e.push(r)}}const v=isNone(l),m=isNone(s);if(v&&m)return calc$1(calcWrapper(c),r,u);const p=solve(calc$1(calcWrapper(c),r,u));if(-1===p)return-1;if(v){const n=solve(calc$1(calcWrapper(s),r,u));return-1===n?-1:solveMin((C=p,g=n,new o([f.Function,"min(",-1,-1,{value:"min"}],[f.CloseParen,")",-1,-1,void 0],[C,new e([f.Comma,",",-1,-1,void 0]),g])),[p,n],u)}if(m){const e=solve(calc$1(calcWrapper(l),r,u));return-1===e?-1:solveMax(maxWrapper(e,p),[e,p],u)}var C,g;const d=solve(calc$1(calcWrapper(l),r,u));if(-1===d)return-1;const D=solve(calc$1(calcWrapper(s),r,u));if(-1===D)return-1;return solveClamp(t,d,p,D,u)}],["cos",function cos(e,n,t){return singleNodeSolver(e,n,t,solveCos)}],["exp",function exp(e,n,t){return singleNodeSolver(e,n,t,solveExp)}],["hypot",function hypot(e,n,t){return variadicNodesSolver(e,e.value,n,t,solveHypot)}],["log",function log(e,n,t){return variadicNodesSolver(e,e.value,n,t,solveLog)}],["max",function max(e,n,t){return variadicNodesSolver(e,e.value,n,t,solveMax)}],["min",function min(e,n,t){return variadicNodesSolver(e,e.value,n,t,solveMin)}],["mod",function mod(e,n,t){return twoCommaSeparatedNodesSolver(e,n,t,solveMod)}],["pow",function pow(e,n,t){return twoCommaSeparatedNodesSolver(e,n,t,solvePow)}],["random",function random(e,n,t){const r=parseRandomValueSharing(e.value.filter((e=>!a(e))),n,t);if(-1===r)return-1;const[u,i]=r,o=variadicArguments(i,n,t);if(-1===o)return-1;const[l,c,s]=o;if(!l||!c)return-1;return solveRandom(e,u,l,c,s,t)}],["rem",function rem(e,n,t){return twoCommaSeparatedNodesSolver(e,n,t,solveRem)}],["round",function round(t,r,u){const i=resolveGlobalsAndConstants([...t.value.filter((e=>!a(e)))],r);let o="",l=!1;const c=[],s=[];{let e=c;for(let t=0;t<i.length;t++){const r=i[t];if(!o&&0===c.length&&0===s.length&&n(r)&&d(r.value)){const e=r.value[4].value.toLowerCase();if(Y.has(e)){o=e;continue}}if(n(r)&&A(r.value)){if(e===s)return-1;if(e===c&&o&&0===c.length)continue;if(e===c){l=!0,e=s;continue}return-1}e.push(r)}}const v=solve(calc$1(calcWrapper(c),r,u));if(-1===v)return-1;l||0!==s.length||s.push(new e([f.Number,"1",-1,-1,{value:1,type:m.Integer}]));const p=solve(calc$1(calcWrapper(s),r,u));if(-1===p)return-1;o||(o="nearest");return solveRound(t,o,v,p,u)}],["sign",function sign(e,n,t){return singleNodeSolver(e,n,t,solveSign)}],["sin",function sin(e,n,t){return singleNodeSolver(e,n,t,solveSin)}],["sqrt",function sqrt(e,n,t){return singleNodeSolver(e,n,t,solveSqrt)}],["tan",function tan(e,n,t){return singleNodeSolver(e,n,t,solveTan)}]]);function calc$1(e,t,r){const o=resolveGlobalsAndConstants([...e.value.filter((e=>!a(e)))],t);if(1===o.length&&n(o[0]))return{inputs:[o[0]],operation:unary};let l=0;for(;l<o.length;){const e=o[l];if(u(e)&&N(e.startToken)){const n=calc$1(e,t,r);if(-1===n)return-1;o.splice(l,1,n)}else if(i(e)){const n=j.get(e.getName().toLowerCase());if(!n)return-1;const a=n(e,t,r);if(-1===a)return-1;o.splice(l,1,a)}else l++}if(l=0,1===o.length&&isCalculation(o[0]))return o[0];for(;l<o.length;){const e=o[l];if(!e||!n(e)&&!isCalculation(e)){l++;continue}const t=o[l+1];if(!t||!n(t)){l++;continue}const r=t.value;if(!B(r)||"*"!==r[4].value&&"/"!==r[4].value){l++;continue}const a=o[l+2];if(!a||!n(a)&&!isCalculation(a))return-1;"*"!==r[4].value?"/"!==r[4].value?l++:o.splice(l,3,{inputs:[e,a],operation:division}):o.splice(l,3,{inputs:[e,a],operation:multiplication})}if(l=0,1===o.length&&isCalculation(o[0]))return o[0];for(;l<o.length;){const e=o[l];if(!e||!n(e)&&!isCalculation(e)){l++;continue}const t=o[l+1];if(!t||!n(t)){l++;continue}const r=t.value;if(!B(r)||"+"!==r[4].value&&"-"!==r[4].value){l++;continue}const a=o[l+2];if(!a||!n(a)&&!isCalculation(a))return-1;"+"!==r[4].value?"-"!==r[4].value?l++:o.splice(l,3,{inputs:[e,a],operation:subtraction}):o.splice(l,3,{inputs:[e,a],operation:addition})}return 1===o.length&&isCalculation(o[0])?o[0]:-1}function singleNodeSolver(e,n,t,r){const a=singleArgument(e.value,n,t);return-1===a?-1:r(e,a,t)}function singleArgument(e,n,t){const r=solve(calc$1(calcWrapper(resolveGlobalsAndConstants([...e.filter((e=>!a(e)))],n)),n,t));return-1===r?-1:r}function twoCommaSeparatedNodesSolver(e,n,t,r){const a=twoCommaSeparatedArguments(e.value,n,t);if(-1===a)return-1;const[u,i]=a;return r(e,u,i,t)}function twoCommaSeparatedArguments(e,t,r){const u=resolveGlobalsAndConstants([...e.filter((e=>!a(e)))],t),i=[],o=[];{let e=i;for(let t=0;t<u.length;t++){const r=u[t];if(n(r)&&A(r.value)){if(e===o)return-1;if(e===i){e=o;continue}return-1}e.push(r)}}const l=solve(calc$1(calcWrapper(i),t,r));if(-1===l)return-1;const c=solve(calc$1(calcWrapper(o),t,r));return-1===c?-1:[l,c]}function variadicNodesSolver(e,n,t,r,a){const u=variadicArguments(e.value,t,r);return-1===u?-1:a(e,u,r)}function variadicArguments(e,t,r){const u=resolveGlobalsAndConstants([...e.filter((e=>!a(e)))],t),i=[];{const e=[];let a=[];for(let t=0;t<u.length;t++){const r=u[t];n(r)&&A(r.value)?(e.push(a),a=[]):a.push(r)}e.push(a);for(let n=0;n<e.length;n++){if(0===e[n].length)return-1;const a=solve(calc$1(calcWrapper(e[n]),t,r));if(-1===a)return-1;i.push(a)}}return i}const Y=new Set(["nearest","up","down","to-zero"]);function parseRandomValueSharing(e,t,r){const a={isAuto:!1,dashedIdent:"",fixed:-1,elementShared:!1},u=e[0];if(!n(u)||!d(u.value))return[a,e];for(let u=0;u<e.length;u++){const i=e[u];if(!n(i))return-1;if(A(i.value))return[a,e.slice(u+1)];if(!d(i.value))return-1;const o=i.value[4].value.toLowerCase();if("element-shared"!==o)if("fixed"!==o)if("auto"!==o)if(o.startsWith("--")){if(-1!==a.fixed||a.isAuto)return-1;a.dashedIdent=o}else;else{if(-1!==a.fixed||a.dashedIdent)return-1;a.isAuto=!0}else{if(a.elementShared||a.dashedIdent||a.isAuto)return-1;u++;const n=e[u];if(!n)return-1;const i=solve(calc$1(calcWrapper([n]),t,r));if(-1===i)return-1;if(!C(i.value))return-1;if(i.value[4].value<0||i.value[4].value>1)return-1;a.fixed=Math.max(0,Math.min(i.value[4].value,1-1e-9))}else{if(-1!==a.fixed)return-1;a.elementShared=!0}}return-1}function calcWrapper(e){return new o([f.Function,"calc(",-1,-1,{value:"calc"}],[f.CloseParen,")",-1,-1,void 0],e)}function maxWrapper(n,t){return new o([f.Function,"max(",-1,-1,{value:"max"}],[f.CloseParen,")",-1,-1,void 0],[n,new e([f.Comma,",",-1,-1,void 0]),t])}function patchNaN(n){if(-1===n)return-1;if(i(n))return n;const t=n.value;return D(t)&&Number.isNaN(t[4].value)?C(t)?new o([f.Function,"calc(",t[2],t[3],{value:"calc"}],[f.CloseParen,")",t[2],t[3],void 0],[new e([f.Ident,"NaN",t[2],t[3],{value:"NaN"}])]):v(t)?new o([f.Function,"calc(",t[2],t[3],{value:"calc"}],[f.CloseParen,")",t[2],t[3],void 0],[new e([f.Ident,"NaN",t[2],t[3],{value:"NaN"}]),new l([[f.Whitespace," ",t[2],t[3],void 0]]),new e([f.Delim,"*",t[2],t[3],{value:"*"}]),new l([[f.Whitespace," ",t[2],t[3],void 0]]),new e([f.Dimension,"1"+t[4].unit,t[2],t[3],{value:1,type:m.Integer,unit:t[4].unit}])]):g(t)?new o([f.Function,"calc(",t[2],t[3],{value:"calc"}],[f.CloseParen,")",t[2],t[3],void 0],[new e([f.Ident,"NaN",t[2],t[3],{value:"NaN"}]),new l([[f.Whitespace," ",t[2],t[3],void 0]]),new e([f.Delim,"*",t[2],t[3],{value:"*"}]),new l([[f.Whitespace," ",t[2],t[3],void 0]]),new e([f.Percentage,"1%",t[2],t[3],{value:1}])]):-1:n}function patchInfinity(n){if(-1===n)return-1;if(i(n))return n;const t=n.value;if(!D(t))return n;if(Number.isFinite(t[4].value)||Number.isNaN(t[4].value))return n;let r="";return Number.NEGATIVE_INFINITY===t[4].value&&(r="-"),C(t)?new o([f.Function,"calc(",t[2],t[3],{value:"calc"}],[f.CloseParen,")",t[2],t[3],void 0],[new e([f.Ident,r+"infinity",t[2],t[3],{value:r+"infinity"}])]):v(t)?new o([f.Function,"calc(",t[2],t[3],{value:"calc"}],[f.CloseParen,")",t[2],t[3],void 0],[new e([f.Ident,r+"infinity",t[2],t[3],{value:r+"infinity"}]),new l([[f.Whitespace," ",t[2],t[3],void 0]]),new e([f.Delim,"*",t[2],t[3],{value:"*"}]),new l([[f.Whitespace," ",t[2],t[3],void 0]]),new e([f.Dimension,"1"+t[4].unit,t[2],t[3],{value:1,type:m.Integer,unit:t[4].unit}])]):new o([f.Function,"calc(",t[2],t[3],{value:"calc"}],[f.CloseParen,")",t[2],t[3],void 0],[new e([f.Ident,r+"infinity",t[2],t[3],{value:r+"infinity"}]),new l([[f.Whitespace," ",t[2],t[3],void 0]]),new e([f.Delim,"*",t[2],t[3],{value:"*"}]),new l([[f.Whitespace," ",t[2],t[3],void 0]]),new e([f.Percentage,"1%",t[2],t[3],{value:1}])])}function patchMinusZero(e){if(-1===e)return-1;if(i(e))return e;const n=e.value;return D(n)&&Object.is(-0,n[4].value)?("-0"===n[1]||(g(n)?n[1]="-0%":v(n)?n[1]="-0"+n[4].unit:n[1]="-0"),e):e}function patchPrecision(e,n=13){if(-1===e)return-1;if(n<=0)return e;if(i(e))return e;const t=e.value;if(!D(t))return e;if(Number.isInteger(t[4].value))return e;const r=Number(t[4].value.toFixed(n)).toString();return C(t)?t[1]=r:g(t)?t[1]=r+"%":v(t)&&(t[1]=r+t[4].unit),e}function patchCanonicalUnit(e){return-1===e?-1:i(e)?e:v(e.value)?(e.value=toCanonicalUnit(e.value),e):e}function patchCalcResult(e,n){let t=e;return n?.toCanonicalUnits&&(t=patchCanonicalUnit(t)),t=patchPrecision(t,n?.precision),t=patchMinusZero(t),n?.censorIntoStandardRepresentableValues||(t=patchNaN(t),t=patchInfinity(t)),t}function tokenizeGlobals(e){const n=new Map;if(!e)return n;for(const[t,r]of e)if(h(r))n.set(t,r);else if("string"!=typeof r);else{const e=F({css:r}),a=e.nextToken();if(e.nextToken(),!e.endOfFile())continue;if(!D(a))continue;n.set(t,a)}return n}function calc(e,n){return calcFromComponentValues(c(b({css:e}),{}),n).map((e=>e.map((e=>w(...e.tokens()))).join(""))).join(",")}function calcFromComponentValues(e,n){const t=tokenizeGlobals(n?.globals);return s(e,(e=>{if(!i(e))return;const r=j.get(e.getName().toLowerCase());if(!r)return;const a=patchCalcResult(solve(r(e,t,n??{})),n);return-1!==a?a:void 0}))}const _=new Set(j.keys());export{calc,calcFromComponentValues,_ as mathFunctionNames};
