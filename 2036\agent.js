
/**
 * 🤖 Enhanced AI Agent - Complete Autonomous Assistant
 * ⚡ Real-time streaming, smart tool orchestration, advanced memory
 * 🎨 Beautiful CLI interface with context awareness
 * 🛠️ Comprehensive tool suite for development and automation
 */

const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { exec, spawn } = require('child_process');
const readline = require('readline');
const os = require('os');
const { promisify } = require('util');
const execAsync = promisify(exec);
const chalk = require('chalk');
const { v4: uuidv4 } = require('uuid');
const cheerio = require('cheerio');
const crypto = require('crypto');

// Platform detection
const IS_WINDOWS = process.platform === 'win32';
const IS_MAC = process.platform === 'darwin';
const IS_LINUX = process.platform === 'linux';

// ═══════════════════════════════════════════════════════════════════════════════
// 🔧 ENHANCED CONFIGURATION & CORE SETUP
// ═══════════════════════════════════════════════════════════════════════════════

const CONFIG_DIR = path.join(os.homedir(), '.enhanced-ai-agent');
const MEMORY_DB_PATH = path.join(CONFIG_DIR, 'memory.json');

const DEFAULT_CONFIG = {
  apiKey: process.env.GEMINI_API_KEY || 'AIzaSyDOE7pTDMrVPGmNlkuCpgFav-85hjsrTtw',
  model: process.env.GEMINI_MODEL || 'gemini-2.0-flash-exp',
  temperature: 0.7,
  maxOutputTokens: 8192,
  topK: 40,
  topP: 0.95,
  streamingEnabled: true,
  maxHistorySize: 50,
  contextWindowSize: 32000,
  debug: process.env.DEBUG === 'true',
  autoSaveMemory: true,
  defaultTimeout: 120000,
  maxRetries: 3,
  rateLimitDelay: 1000
};

let config = { ...DEFAULT_CONFIG };
let genAI = null;
let model = null;

// Enhanced Memory Database
let memoryDB = {
  conversations: [],
  context: {},
  toolUsage: {},
  userPreferences: {},
  projectContext: {},
  learningData: {},
  lastUpdated: new Date().toISOString()
};

// ═══════════════════════════════════════════════════════════════════════════════
// 🎨 ENHANCED CLI INTERFACE & STREAMING COMPONENTS
// ═══════════════════════════════════════════════════════════════════════════════

class StreamingInterface {
  constructor() {
    this.isStreaming = false;
    this.currentLine = '';
  }

  // Beautiful gradient text
  gradient(text, colors = ['#ff6b6b', '#4ecdc4', '#45b7d1']) {
    const length = text.length;
    let result = '';
    for (let i = 0; i < length; i++) {
      const colorIndex = Math.floor((i / length) * (colors.length - 1));
      const color = colors[colorIndex];
      result += chalk.hex(color)(text[i]);
    }
    return result;
  }

  // Animated thinking indicator
  async showThinking(message = 'Thinking') {
    const frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
    let i = 0;

    return setInterval(() => {
      process.stdout.write(`\r${chalk.cyan(frames[i % frames.length])} ${chalk.yellow(message)}...`);
      i++;
    }, 100);
  }

  // Stop thinking indicator
  stopThinking(interval) {
    if (interval) {
      clearInterval(interval);
      process.stdout.write('\r' + ' '.repeat(50) + '\r');
    }
  }

  // Stream text character by character
  async streamText(text, delay = 20) {
    this.isStreaming = true;
    for (const char of text) {
      if (!this.isStreaming) break;
      process.stdout.write(char);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    this.isStreaming = false;
  }

  // Progress bar
  showProgress(current, total, label = 'Progress') {
    const percentage = Math.round((current / total) * 100);
    const filled = Math.round((current / total) * 20);
    const empty = 20 - filled;
    const bar = '█'.repeat(filled) + '░'.repeat(empty);
    process.stdout.write(`\r${chalk.blue(label)}: [${chalk.green(bar)}] ${percentage}%`);
  }

  // Enhanced prompt
  getPrompt() {
    const time = new Date().toLocaleTimeString();
    return this.gradient(`\n┌─[${time}] `) + chalk.white('AI Agent') + this.gradient(' ─┐\n└─> ');
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🧠 ADVANCED MEMORY & CONTEXT MANAGEMENT
// ═══════════════════════════════════════════════════════════════════════════════

class MemoryManager {
  constructor() {
    this.memoryPath = path.join(CONFIG_DIR, 'memory.json');
    this.ensureDirectories();
    this.loadMemory();
  }

  ensureDirectories() {
    if (!fs.existsSync(CONFIG_DIR)) {
      fs.mkdirSync(CONFIG_DIR, { recursive: true });
    }
  }

  loadMemory() {
    try {
      if (fs.existsSync(this.memoryPath)) {
        const data = fs.readFileSync(this.memoryPath, 'utf8');
        memoryDB = { ...memoryDB, ...JSON.parse(data) };
      }
    } catch (error) {
      console.error(chalk.red('Error loading memory:'), error.message);
    }
  }

  saveMemory() {
    try {
      memoryDB.lastUpdated = new Date().toISOString();
      fs.writeFileSync(this.memoryPath, JSON.stringify(memoryDB, null, 2));
    } catch (error) {
      console.error(chalk.red('Error saving memory:'), error.message);
    }
  }

  addConversation(userInput, agentResponse, tools = []) {
    const conversation = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      userInput,
      agentResponse,
      toolsUsed: tools,
      context: this.getCurrentContext()
    };

    memoryDB.conversations.push(conversation);

    // Keep only recent conversations
    if (memoryDB.conversations.length > config.maxHistorySize) {
      memoryDB.conversations = memoryDB.conversations.slice(-config.maxHistorySize);
    }

    this.updateContext(userInput, agentResponse);
    if (config.autoSaveMemory) this.saveMemory();
  }

  getCurrentContext() {
    return {
      workingDirectory: process.cwd(),
      timestamp: new Date().toISOString(),
      platform: process.platform,
      recentFiles: this.getRecentFiles()
    };
  }

  getRecentFiles() {
    try {
      const files = fs.readdirSync(process.cwd())
        .filter(file => fs.statSync(file).isFile())
        .slice(0, 10);
      return files;
    } catch {
      return [];
    }
  }

  updateContext(userInput, agentResponse) {
    // Extract and store important context
    const keywords = this.extractKeywords(userInput + ' ' + agentResponse);
    keywords.forEach(keyword => {
      if (!memoryDB.context[keyword]) {
        memoryDB.context[keyword] = { count: 0, lastUsed: null };
      }
      memoryDB.context[keyword].count++;
      memoryDB.context[keyword].lastUsed = new Date().toISOString();
    });
  }

  extractKeywords(text) {
    // Simple keyword extraction
    return text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3)
      .slice(0, 20);
  }

  getRelevantContext(query) {
    const queryKeywords = this.extractKeywords(query);
    const relevantConversations = memoryDB.conversations
      .filter(conv => {
        const convText = conv.userInput + ' ' + conv.agentResponse;
        return queryKeywords.some(keyword =>
          convText.toLowerCase().includes(keyword.toLowerCase())
        );
      })
      .slice(-5); // Get last 5 relevant conversations

    return relevantConversations;
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🛠️ COMPREHENSIVE TOOL EXECUTION ENGINE
// ═══════════════════════════════════════════════════════════════════════════════

class ToolExecutor {
  constructor(memoryManager, streamingInterface) {
    this.memory = memoryManager;
    this.ui = streamingInterface;
    this.runningCommands = new Map();
  }

  // 📁 File System Tools
  async createFile(params) {
    const { path: filePath, content = '', overwrite = false } = params;

    try {
      if (fs.existsSync(filePath) && !overwrite) {
        return { success: false, error: 'File already exists. Use overwrite=true to replace.' };
      }

      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      fs.writeFileSync(filePath, content, 'utf8');
      return { success: true, message: `File created: ${filePath}`, path: filePath };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async editFile(params) {
    const { path: filePath, operation, content, lineNumber, searchText, replaceText } = params;

    try {
      if (!fs.existsSync(filePath)) {
        return { success: false, error: 'File does not exist' };
      }

      let fileContent = fs.readFileSync(filePath, 'utf8');
      let lines = fileContent.split('\n');

      switch (operation) {
        case 'insert':
          if (lineNumber !== undefined) {
            lines.splice(lineNumber, 0, content);
          } else {
            lines.push(content);
          }
          break;

        case 'replace':
          if (searchText && replaceText !== undefined) {
            fileContent = fileContent.replace(new RegExp(searchText, 'g'), replaceText);
            lines = fileContent.split('\n');
          }
          break;

        case 'delete':
          if (lineNumber !== undefined) {
            lines.splice(lineNumber, 1);
          }
          break;

        case 'append':
          lines.push(content);
          break;

        default:
          return { success: false, error: 'Invalid operation' };
      }

      fs.writeFileSync(filePath, lines.join('\n'), 'utf8');
      return { success: true, message: `File edited: ${filePath}` };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async readFile(params) {
    const { path: filePath, startLine, endLine, encoding = 'utf8' } = params;

    try {
      if (!fs.existsSync(filePath)) {
        return { success: false, error: 'File does not exist' };
      }

      const content = fs.readFileSync(filePath, encoding);

      if (startLine !== undefined || endLine !== undefined) {
        const lines = content.split('\n');
        const start = startLine || 0;
        const end = endLine || lines.length;
        const selectedLines = lines.slice(start, end + 1);
        return { success: true, content: selectedLines.join('\n'), lines: selectedLines.length };
      }

      return { success: true, content, size: content.length };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async listDirectory(params) {
    const { path: dirPath = process.cwd(), recursive = false, includeHidden = false } = params;

    try {
      if (!fs.existsSync(dirPath)) {
        return { success: false, error: 'Directory does not exist' };
      }

      const items = [];

      const scanDirectory = (currentPath, depth = 0) => {
        const entries = fs.readdirSync(currentPath);

        for (const entry of entries) {
          if (!includeHidden && entry.startsWith('.')) continue;

          const fullPath = path.join(currentPath, entry);
          const stats = fs.statSync(fullPath);
          const relativePath = path.relative(dirPath, fullPath);

          items.push({
            name: entry,
            path: relativePath,
            fullPath,
            type: stats.isDirectory() ? 'directory' : 'file',
            size: stats.size,
            modified: stats.mtime,
            depth
          });

          if (recursive && stats.isDirectory() && depth < 3) {
            scanDirectory(fullPath, depth + 1);
          }
        }
      };

      scanDirectory(dirPath);
      return { success: true, items, count: items.length };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async fileSearch(params) {
    const { pattern, directory = process.cwd(), recursive = true, fileTypes = [] } = params;

    try {
      const results = [];

      const searchInDirectory = (dir) => {
        const entries = fs.readdirSync(dir);

        for (const entry of entries) {
          const fullPath = path.join(dir, entry);
          const stats = fs.statSync(fullPath);

          if (stats.isDirectory() && recursive) {
            searchInDirectory(fullPath);
          } else if (stats.isFile()) {
            const ext = path.extname(entry).toLowerCase();
            if (fileTypes.length === 0 || fileTypes.includes(ext)) {
              if (entry.toLowerCase().includes(pattern.toLowerCase())) {
                results.push({
                  name: entry,
                  path: fullPath,
                  relativePath: path.relative(directory, fullPath),
                  size: stats.size,
                  modified: stats.mtime
                });
              }
            }
          }
        }
      };

      searchInDirectory(directory);
      return { success: true, results, count: results.length };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async grepSearch(params) {
    const { query, directory = process.cwd(), fileTypes = ['.js', '.ts', '.py', '.txt', '.md'], caseSensitive = false } = params;

    try {
      const results = [];
      const flags = caseSensitive ? 'g' : 'gi';
      const regex = new RegExp(query, flags);

      const searchInFile = (filePath) => {
        try {
          const content = fs.readFileSync(filePath, 'utf8');
          const lines = content.split('\n');

          lines.forEach((line, index) => {
            if (regex.test(line)) {
              results.push({
                file: filePath,
                relativePath: path.relative(directory, filePath),
                lineNumber: index + 1,
                line: line.trim(),
                match: line.match(regex)
              });
            }
          });
        } catch (error) {
          // Skip files that can't be read
        }
      };

      const searchDirectory = (dir) => {
        const entries = fs.readdirSync(dir);

        for (const entry of entries) {
          const fullPath = path.join(dir, entry);
          const stats = fs.statSync(fullPath);

          if (stats.isDirectory() && !entry.startsWith('.')) {
            searchDirectory(fullPath);
          } else if (stats.isFile()) {
            const ext = path.extname(entry).toLowerCase();
            if (fileTypes.includes(ext)) {
              searchInFile(fullPath);
            }
          }
        }
      };

      searchDirectory(directory);
      return { success: true, results, count: results.length };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // 💻 Terminal & Command Tools
  async runInTerminal(params) {
    const { command, cwd = process.cwd(), timeout = 30000, background = false } = params;
    const commandId = uuidv4();

    try {
      if (background) {
        const child = spawn(command, {
          shell: true,
          cwd,
          detached: true,
          stdio: 'pipe'
        });

        this.runningCommands.set(commandId, {
          process: child,
          command,
          startTime: Date.now(),
          stdout: '',
          stderr: ''
        });

        child.stdout.on('data', (data) => {
          const cmd = this.runningCommands.get(commandId);
          if (cmd) cmd.stdout += data.toString();
        });

        child.stderr.on('data', (data) => {
          const cmd = this.runningCommands.get(commandId);
          if (cmd) cmd.stderr += data.toString();
        });

        return { success: true, commandId, message: 'Command started in background' };
      } else {
        const { stdout, stderr } = await execAsync(command, { cwd, timeout });
        return { success: true, stdout, stderr, command };
      }
    } catch (error) {
      return { success: false, error: error.message, command };
    }
  }

  async getTerminalOutput(params) {
    const { commandId } = params;
    const cmd = this.runningCommands.get(commandId);

    if (!cmd) {
      return { success: false, error: 'Command not found' };
    }

    return {
      success: true,
      stdout: cmd.stdout,
      stderr: cmd.stderr,
      running: !cmd.process.killed,
      startTime: cmd.startTime
    };
  }

  // 🌐 Web & Search Tools (without puppeteer)
  async fetchWebpage(params) {
    const { url, timeout = 10000, headers = {} } = params;

    try {
      const response = await axios.get(url, {
        timeout,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          ...headers
        }
      });

      const $ = cheerio.load(response.data);

      // Extract useful content
      const title = $('title').text().trim();
      const description = $('meta[name="description"]').attr('content') || '';
      const text = $('body').text().replace(/\s+/g, ' ').trim().substring(0, 5000);

      return {
        success: true,
        url,
        title,
        description,
        content: text,
        statusCode: response.status,
        size: response.data.length
      };
    } catch (error) {
      return { success: false, error: error.message, url };
    }
  }

  async semanticWebSearch(params) {
    const { query, maxResults = 10, domain = null } = params;

    try {
      // Use DuckDuckGo search (no API key required)
      const searchUrl = `https://duckduckgo.com/html/?q=${encodeURIComponent(query)}${domain ? `+site:${domain}` : ''}`;

      const response = await axios.get(searchUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      const $ = cheerio.load(response.data);
      const results = [];

      $('.result').each((i, element) => {
        if (i >= maxResults) return false;

        const $el = $(element);
        const title = $el.find('.result__title a').text().trim();
        const url = $el.find('.result__title a').attr('href');
        const snippet = $el.find('.result__snippet').text().trim();

        if (title && url) {
          results.push({ title, url, snippet });
        }
      });

      return { success: true, query, results, count: results.length };
    } catch (error) {
      return { success: false, error: error.message, query };
    }
  }

  // 🧠 AI-Powered Tools
  async naturalLanguageToCode(params) {
    const { description, language = 'javascript', style = 'modern' } = params;

    try {
      const prompt = `Convert this natural language description to ${language} code:

Description: ${description}
Style: ${style}

Requirements:
- Write clean, readable code
- Include comments
- Follow best practices
- Make it production-ready

Code:`;

      const result = await model.generateContent(prompt);
      const code = result.response.text();

      return { success: true, description, language, code };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async intentRecognition(params) {
    const { text } = params;

    try {
      const prompt = `Analyze this user input and determine the intent and required actions:

User Input: "${text}"

Respond with JSON containing:
{
  "intent": "primary intent",
  "confidence": 0.95,
  "entities": ["extracted", "entities"],
  "suggestedActions": ["action1", "action2"],
  "toolsNeeded": ["tool1", "tool2"]
}`;

      const result = await model.generateContent(prompt);
      const analysis = JSON.parse(result.response.text());

      return { success: true, text, analysis };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🤖 AI INTEGRATION & SMART ORCHESTRATION
// ═══════════════════════════════════════════════════════════════════════════════

class AIOrchestrator {
  constructor(memoryManager, streamingInterface, toolExecutor) {
    this.memory = memoryManager;
    this.ui = streamingInterface;
    this.tools = toolExecutor;
    this.initializeAI();
  }

  initializeAI() {
    try {
      if (!config.apiKey) {
        throw new Error('API key is required. Set GEMINI_API_KEY environment variable.');
      }

      genAI = new GoogleGenerativeAI(config.apiKey);
      model = genAI.getGenerativeModel({
        model: config.model,
        generationConfig: {
          temperature: config.temperature,
          maxOutputTokens: config.maxOutputTokens,
          topK: config.topK,
          topP: config.topP
        }
      });

      console.log(chalk.green('✅ AI model initialized successfully'));
    } catch (error) {
      console.error(chalk.red('❌ Failed to initialize AI:'), error.message);
      process.exit(1);
    }
  }

  async planAndExecute(userInput) {
    const thinkingInterval = this.ui.showThinking('Analyzing your request');

    try {
      // Get relevant context from memory
      const context = this.memory.getRelevantContext(userInput);

      // Analyze intent and plan actions
      const plan = await this.createExecutionPlan(userInput, context);
      this.ui.stopThinking(thinkingInterval);

      if (!plan.success) {
        return { success: false, error: plan.error };
      }

      // Display plan
      console.log(chalk.cyan('\n🎯 Understanding: ') + chalk.white(plan.intent));
      console.log(chalk.cyan('📋 Plan: ') + chalk.white(plan.steps.length + ' steps'));

      // Execute plan steps
      const results = [];
      for (let i = 0; i < plan.steps.length; i++) {
        const step = plan.steps[i];
        console.log(chalk.yellow(`\n⚡ Step ${i + 1}: ${step.description}`));

        const stepResult = await this.executeStep(step);
        results.push(stepResult);

        if (stepResult.success) {
          console.log(chalk.green('✅ Completed'));
        } else {
          console.log(chalk.red('❌ Failed: ' + stepResult.error));
        }
      }

      // Generate final response
      const response = await this.generateResponse(userInput, plan, results);

      // Save to memory
      this.memory.addConversation(userInput, response, plan.steps.map(s => s.tool));

      return { success: true, response, plan, results };
    } catch (error) {
      this.ui.stopThinking(thinkingInterval);
      return { success: false, error: error.message };
    }
  }

  async createExecutionPlan(userInput, context) {
    try {
      const prompt = `You are an advanced AI assistant that can plan and execute tasks using various tools.

User Request: "${userInput}"

Context from previous conversations:
${context.map(c => `- ${c.userInput}: ${c.agentResponse.substring(0, 200)}...`).join('\n')}

Available Tools:
- createFile: Create new files
- editFile: Edit existing files
- readFile: Read file contents
- listDirectory: List directory contents
- fileSearch: Search for files by name
- grepSearch: Search text within files
- runInTerminal: Execute shell commands
- fetchWebpage: Get webpage content
- semanticWebSearch: Search the web
- naturalLanguageToCode: Convert descriptions to code
- intentRecognition: Analyze user intent

Create a step-by-step execution plan. Respond with JSON:
{
  "intent": "what the user wants to accomplish",
  "confidence": 0.95,
  "steps": [
    {
      "tool": "toolName",
      "description": "what this step does",
      "parameters": { "param": "value" },
      "reasoning": "why this step is needed"
    }
  ]
}`;

      const result = await model.generateContent(prompt);
      const planText = result.response.text();

      // Extract JSON from response
      const jsonMatch = planText.match(/```json\n([\s\S]*?)\n```/) || planText.match(/({[\s\S]*})/);
      if (!jsonMatch) {
        throw new Error('Could not parse execution plan');
      }

      const plan = JSON.parse(jsonMatch[1]);
      return { success: true, ...plan };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async executeStep(step) {
    try {
      const { tool, parameters } = step;

      // Route to appropriate tool
      switch (tool) {
        case 'createFile':
          return await this.tools.createFile(parameters);
        case 'editFile':
          return await this.tools.editFile(parameters);
        case 'readFile':
          return await this.tools.readFile(parameters);
        case 'listDirectory':
          return await this.tools.listDirectory(parameters);
        case 'fileSearch':
          return await this.tools.fileSearch(parameters);
        case 'grepSearch':
          return await this.tools.grepSearch(parameters);
        case 'runInTerminal':
          return await this.tools.runInTerminal(parameters);
        case 'fetchWebpage':
          return await this.tools.fetchWebpage(parameters);
        case 'semanticWebSearch':
          return await this.tools.semanticWebSearch(parameters);
        case 'naturalLanguageToCode':
          return await this.tools.naturalLanguageToCode(parameters);
        case 'intentRecognition':
          return await this.tools.intentRecognition(parameters);
        default:
          return { success: false, error: `Unknown tool: ${tool}` };
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async generateResponse(userInput, plan, results) {
    try {
      const prompt = `Generate a helpful response based on the execution results.

User Request: "${userInput}"
Plan Intent: ${plan.intent}

Execution Results:
${results.map((r, i) => `Step ${i + 1}: ${r.success ? 'Success' : 'Failed'} - ${JSON.stringify(r).substring(0, 200)}...`).join('\n')}

Provide a conversational, helpful response that:
1. Summarizes what was accomplished
2. Highlights any important findings
3. Suggests next steps if appropriate
4. Mentions any errors and how to fix them

Response:`;

      const result = await model.generateContent(prompt);
      return result.response.text();
    } catch (error) {
      return `I completed the requested tasks. ${results.filter(r => r.success).length} out of ${results.length} steps succeeded.`;
    }
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🚀 MAIN INTERACTIVE SYSTEM
// ═══════════════════════════════════════════════════════════════════════════════

class EnhancedAgent {
  constructor() {
    this.ui = new StreamingInterface();
    this.memory = new MemoryManager();
    this.tools = new ToolExecutor(this.memory, this.ui);
    this.ai = new AIOrchestrator(this.memory, this.ui, this.tools);
    this.isRunning = false;
  }

  async start() {
    console.clear();

    // Beautiful startup banner
    console.log(this.ui.gradient('╔══════════════════════════════════════════════════════════════╗'));
    console.log(this.ui.gradient('║                    🤖 Enhanced AI Agent                     ║'));
    console.log(this.ui.gradient('║              ⚡ Real-time • Smart • Powerful ⚡              ║'));
    console.log(this.ui.gradient('╚══════════════════════════════════════════════════════════════╝'));

    console.log(chalk.cyan('\n🎯 Ready to assist with:'));
    console.log(chalk.white('  • File operations & code editing'));
    console.log(chalk.white('  • Terminal commands & automation'));
    console.log(chalk.white('  • Web scraping & research'));
    console.log(chalk.white('  • Code generation & analysis'));
    console.log(chalk.white('  • Project management & workflows'));

    console.log(chalk.yellow('\n💡 Special commands:'));
    console.log(chalk.gray('  !clear    - Clear conversation history'));
    console.log(chalk.gray('  !memory   - Show memory statistics'));
    console.log(chalk.gray('  !help     - Show help information'));
    console.log(chalk.gray('  !exit     - Exit the application'));

    this.isRunning = true;
    await this.interactiveLoop();
  }

  async interactiveLoop() {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: this.ui.getPrompt()
    });

    rl.prompt();

    rl.on('line', async (input) => {
      const trimmedInput = input.trim();

      if (!trimmedInput) {
        rl.prompt();
        return;
      }

      // Handle special commands
      if (await this.handleSpecialCommands(trimmedInput, rl)) {
        return;
      }

      try {
        console.log(chalk.cyan('\n🤔 Processing your request...'));

        // Execute the request
        const result = await this.ai.planAndExecute(trimmedInput);

        if (result.success) {
          console.log(chalk.green('\n🎉 Response:'));
          await this.ui.streamText(result.response, 30);
        } else {
          console.log(chalk.red('\n❌ Error: ') + result.error);
        }

      } catch (error) {
        console.log(chalk.red('\n💥 Unexpected error: ') + error.message);
      }

      console.log('\n');
      rl.prompt();
    });

    rl.on('close', () => {
      console.log(chalk.green('\n👋 Goodbye! Thanks for using Enhanced AI Agent!'));
      process.exit(0);
    });

    // Handle Ctrl+C gracefully
    process.on('SIGINT', () => {
      console.log(chalk.yellow('\n\n⚠️  Shutting down gracefully...'));
      this.memory.saveMemory();
      process.exit(0);
    });
  }

  async handleSpecialCommands(input, rl) {
    const command = input.toLowerCase();

    switch (command) {
      case '!exit':
      case 'exit':
        console.log(chalk.green('👋 Goodbye!'));
        this.memory.saveMemory();
        rl.close();
        return true;

      case '!clear':
        console.clear();
        memoryDB.conversations = [];
        this.memory.saveMemory();
        console.log(chalk.green('🧹 Conversation history cleared!'));
        rl.prompt();
        return true;

      case '!memory':
        this.showMemoryStats();
        rl.prompt();
        return true;

      case '!help':
        this.showHelp();
        rl.prompt();
        return true;

      default:
        return false;
    }
  }

  showMemoryStats() {
    console.log(chalk.cyan('\n📊 Memory Statistics:'));
    console.log(chalk.white(`  Conversations: ${memoryDB.conversations.length}`));
    console.log(chalk.white(`  Context entries: ${Object.keys(memoryDB.context).length}`));
    console.log(chalk.white(`  Tool usage records: ${Object.keys(memoryDB.toolUsage).length}`));
    console.log(chalk.white(`  Last updated: ${memoryDB.lastUpdated}`));
  }

  showHelp() {
    console.log(chalk.cyan('\n📚 Enhanced AI Agent Help:'));
    console.log(chalk.white('\n🎯 What I can do:'));
    console.log(chalk.gray('  • Create, edit, and analyze files'));
    console.log(chalk.gray('  • Execute terminal commands'));
    console.log(chalk.gray('  • Search and scrape web content'));
    console.log(chalk.gray('  • Generate code from descriptions'));
    console.log(chalk.gray('  • Analyze and understand your intent'));
    console.log(chalk.gray('  • Remember context across conversations'));

    console.log(chalk.white('\n💬 How to interact:'));
    console.log(chalk.gray('  • Just type what you want to do in natural language'));
    console.log(chalk.gray('  • I\'ll automatically plan and execute the steps'));
    console.log(chalk.gray('  • I remember our conversation for better context'));

    console.log(chalk.white('\n🔧 Special commands:'));
    console.log(chalk.gray('  • !clear - Clear conversation history'));
    console.log(chalk.gray('  • !memory - Show memory statistics'));
    console.log(chalk.gray('  • !help - Show this help'));
    console.log(chalk.gray('  • !exit - Exit the application'));
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🎬 APPLICATION STARTUP
// ═══════════════════════════════════════════════════════════════════════════════

async function main() {
  try {
    // Ensure config directory exists
    if (!fs.existsSync(CONFIG_DIR)) {
      fs.mkdirSync(CONFIG_DIR, { recursive: true });
    }

    // Create and start the enhanced agent
    const agent = new EnhancedAgent();
    await agent.start();
  } catch (error) {
    console.error(chalk.red('💥 Failed to start Enhanced AI Agent:'), error.message);
    console.error(chalk.yellow('🔧 Please check your configuration and try again.'));
    process.exit(1);
  }
}

// Handle uncaught exceptions gracefully
process.on('uncaughtException', (error) => {
  console.error(chalk.red('\n💥 Uncaught Exception:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('\n💥 Unhandled Rejection at:'), promise, chalk.red('reason:'), reason);
  process.exit(1);
});

// Start the application if this file is run directly
if (require.main === module) {
  main().catch(error => {
    console.error(chalk.red('💥 Startup error:'), error.message);
    process.exit(1);
  });
}

// All old functions removed - functionality moved to enhanced classes above

// ═══════════════════════════════════════════════════════════════════════════════
// 🎯 EXPORT FOR MODULE USAGE
// ═══════════════════════════════════════════════════════════════════════════════

module.exports = {
  EnhancedAgent,
  MemoryManager,
  ToolExecutor,
  AIOrchestrator,
  StreamingInterface,
  config,
  main
};



// Load memories and deployments
loadMemories();
loadDeployments();

// Interactive mode function
async function interactiveMode() {
  console.log(chalk.green('=== Gemini Advanced Agent Interactive Mode ==='));
  console.log(chalk.yellow('Type your prompts and the agent will automatically select tools and respond.'));
  console.log(chalk.yellow('Type "exit" or Ctrl+C to quit.'));
  console.log(chalk.yellow('Special commands:'));
  console.log(chalk.yellow('  !clear - Clear conversation history'));
  console.log(chalk.yellow('  !config - Show current configuration'));
  console.log(chalk.yellow('  !help - Show this help message'));

  // Initialize AI and conversation manager
  initializeAI();
  const conversationManager = new ConversationManager();

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    prompt: config.userPrompt
  });

  rl.prompt();

  rl.on('line', async (line) => {
    const input = line.trim();

    // Handle special commands
    if (input.toLowerCase() === 'exit') {
      console.log(chalk.green('Goodbye!'));
      rl.close();
      return;
    } else if (input.toLowerCase() === '!clear') {
      conversationManager.clearHistory();
      console.log(chalk.green('Conversation history cleared.'));
      rl.prompt();
      return;
    } else if (input.toLowerCase() === '!config') {
      console.log(chalk.cyan('Current Configuration:'));
      const safeConfig = { ...config };
      // Mask API key for security
      if (safeConfig.apiKey) {
        safeConfig.apiKey = safeConfig.apiKey.substring(0, 4) + '...' +
                           safeConfig.apiKey.substring(safeConfig.apiKey.length - 4);
      }
      console.log(JSON.stringify(safeConfig, null, 2));
      rl.prompt();
      return;
    } else if (input.toLowerCase() === '!help') {
      console.log(chalk.green('=== Gemini Advanced Agent Help ==='));
      console.log(chalk.yellow('Type your prompts and the agent will automatically select tools and respond.'));
      console.log(chalk.yellow('Special commands:'));
      console.log(chalk.yellow('  !clear - Clear conversation history'));
      console.log(chalk.yellow('  !config - Show current configuration'));
      console.log(chalk.yellow('  !help - Show this help message'));
      console.log(chalk.yellow('  exit - Exit the application'));
      rl.prompt();
      return;
    }

    // Add user message to history
    conversationManager.addMessage('user', input);

    try {
      spinner.start('Thinking...');

      // Generate a plan using the AI
      const planPrompt = `
User's request: "${input}"

You are an advanced autonomous agent that can use various tools to complete tasks.
First, analyze what the user is asking for and determine which tool(s) would be most appropriate.

Available tools:
1. edit_file - Edit or create a file
   - targetFile: (REQUIRED) path to the file to edit or create
   - instruction: instructions for editing (required when editing an existing file)
   - content: content for creating a new file (required when creating a new file)
   - codeEdit: specific code edits to make (optional)
   - codeMarkdownLanguage: language of the code (optional)
   - targetLintErrorIds: IDs of lint errors to fix (optional)

2. run_command - Execute a shell command
   - commandLine: (REQUIRED) the command to execute
   - cwd: working directory (optional, defaults to current directory)
   - blocking: whether to wait for command completion (optional, defaults to true)
   - waitMsBeforeAsync: milliseconds to wait before returning for async commands
   - safeToAutoRun: whether the command is safe to run automatically

3. grep_search - Search for text in files
   - searchPath: (REQUIRED) directory to search in
   - query: (REQUIRED) text to search for
   - includes: array of file patterns to include (optional)
   - caseInsensitive: whether to ignore case (optional)
   - matchPerLine: limit matches per line (optional)

4. list_dir - List directory contents
   - directoryPath: (REQUIRED) directory to list

5. read_url_content - Fetch content from a URL
   - url: (REQUIRED) URL to fetch content from

6. search_web - Search the web for information
   - query: (REQUIRED) search query
   - domain: specific domain to search within (optional)

7. browser_preview - Open a browser preview for a web application
   - url: (REQUIRED) URL to open
   - name: (REQUIRED) name for the preview

8. deploy_web_app - Deploy a web application
   - framework: (REQUIRED) framework used (React, Vue, Angular, etc.)
   - projectPath: (REQUIRED) path to the project
   - subdomain: custom subdomain for deployment
   - projectId: unique ID for the project

9. check_deploy_status - Check deployment status
   - deploymentId: (REQUIRED) ID of the deployment to check

10. create_memory - Save important context
    - id: (REQUIRED) unique identifier for the memory
    - title: (REQUIRED) title of the memory
    - content: (REQUIRED) content to remember
    - tags: array of tags for categorization
    - corpusNames: array of corpus names
    - action: action to take (create, update, delete)
    - userTriggered: whether the user triggered this

11. find_by_name - Find files by name
    - searchDirectory: (REQUIRED) directory to search in
    - pattern: (REQUIRED) pattern to search for
    - excludes: patterns to exclude
    - type: type of items to find (file, directory, etc.)
    - maxDepth: maximum depth to search
    - extensions: file extensions to include
    - fullPath: whether to return full paths

12. view_file - View file contents
    - absolutePath: (REQUIRED) path to the file
    - startLine: first line to show
    - endLine: last line to show
    - includeSummaryOfOtherLines: whether to include a summary of other lines

13. write_to_file - Create a new file
    - targetFile: (REQUIRED) path to the file to create
    - codeContent: (REQUIRED) content to write
    - emptyFile: whether to create an empty file

14. codebase_search - Search for code snippets
    - query: (REQUIRED) search query
    - targetDirectories: directories to search in

15. command_status - Check command status
    - commandId: (REQUIRED) ID of the command to check
    - outputPriority: priority of output to show
    - outputCharacterCount: maximum characters to show
    - waitDurationSeconds: seconds to wait for completion

16. read_deployment_config - Read deployment configuration
    - projectPath: (REQUIRED) path to the project

17. view_code_item - View a specific code item
    - nodePath: (REQUIRED) path to the code item

18. view_web_document_content_chunk - View a chunk of web content
    - url: (REQUIRED) URL of the web document
    - position: position of the chunk to view

19. suggested_responses - Suggest responses to the user
    - suggestions: (REQUIRED) array of suggested responses

20. analyze_code - Analyze code for patterns, bugs, and improvements
    - codePath: (REQUIRED) path to the code file
    - language: programming language of the code
    - analysisType: type of analysis (security, performance, style)

21. generate_project - Generate a complete project structure
    - projectName: (REQUIRED) name of the project
    - framework: framework to use (React, Vue, Angular, etc.)
    - language: programming language (JavaScript, TypeScript, etc.)
    - features: array of features to include

22. install_dependencies - Install project dependencies
    - packageManager: package manager to use (npm, yarn, pip, etc.)
    - dependencies: array of dependencies to install
    - projectPath: path to the project
    - dev: whether to install as dev dependencies

23. database_operation - Perform database operations
    - operation: (REQUIRED) operation to perform (create, read, update, delete)
    - dbType: database type (MongoDB, MySQL, PostgreSQL, etc.)
    - connectionString: connection string for the database
    - query: query to execute

24. api_integration - Integrate with external APIs
    - apiName: (REQUIRED) name of the API
    - endpoint: endpoint to call
    - method: HTTP method
    - headers: headers to include
    - body: request body

25. deploy_backend - Deploy a backend service
    - servicePath: (REQUIRED) path to the service
    - platform: platform to deploy to (AWS, GCP, Azure, etc.)
    - region: region to deploy to
    - environment: environment to deploy to (dev, staging, prod)

26. setup_ci_cd - Set up CI/CD pipeline
    - provider: (REQUIRED) CI/CD provider (GitHub Actions, Jenkins, etc.)
    - projectPath: path to the project
    - steps: array of pipeline steps

27. analyze_performance - Analyze application performance
    - targetUrl: (REQUIRED) URL to analyze
    - metrics: array of metrics to analyze
    - duration: duration of the analysis

    28. generate_documentation - Generate documentation
    - sourcePath: (REQUIRED) path to the source code
    - outputFormat: format of the documentation (markdown, HTML, etc.)
    - outputPath: path to output the documentation
   38. natural_language_processing - Process and analyze text
      - text: (REQUIRED) text to analyze
      - operation: operation to perform (sentiment, entities, summarization, etc.)
      - language: language of the text

Respond with a structured plan that includes:
1. The user's intention
2. Which tool(s) to use
3. Parameters for each tool
4. Expected outcome

Format your response as valid JSON with the following structure:
{
  "intention": "user's intention",
  "steps": [
    {
      "tool": "tool_name",
      "parameters": { param_object },
      "purpose": "why this step is needed"
    }
  ],
  "expectedOutcome": "what the user will get"
}
`;

      const planResult = await model.generateContent(planPrompt);
      const planText = planResult.response.text();

      // Extract the JSON plan
      const jsonMatch = planText.match(/```json\n([\s\S]*?)\n```/) || planText.match(/({[\s\S]*})/);
      let plan;

      if (jsonMatch) {
        try {
          plan = JSON.parse(jsonMatch[1].trim());
        } catch (e) {
          try {
          plan = JSON.parse(planText);
          } catch (e2) {
            throw new Error("Couldn't parse the plan as valid JSON");
          }
        }
      } else {
        throw new Error("Couldn't parse the plan");
      }

      // Validate plan structure
      if (!plan.intention || !Array.isArray(plan.steps)) {
        throw new Error("Invalid plan structure: missing 'intention' or 'steps' array");
      }

      spinner.succeed('Plan created');
      console.log(chalk.green('\nUnderstanding your request:'));
      console.log(chalk.yellow(plan.intention));

      // Execute each step in the plan
      for (const [index, step] of plan.steps.entries()) {
        console.log(chalk.green(`\nStep ${index + 1}: ${step.purpose || 'Executing next step'}`));

        // Validate step structure
        if (!step.tool) {
          console.log(chalk.red(`Step ${index + 1} is missing the 'tool' field. Skipping.`));
          continue;
        }

        if (!step.parameters) {
          console.log(chalk.red(`Step ${index + 1} is missing the 'parameters' field. Skipping.`));
          continue;
        }

        spinner.start(`Executing: ${step.tool}`);

        // Execute the appropriate tool
        let result;
        switch (step.tool) {
          case 'edit_file':
            result = await executeEditFile(step.parameters);
            break;
          case 'run_command':
            result = await executeRunCommand(step.parameters);
            break;
          case 'grep_search':
            result = await executeGrepSearch(step.parameters);
            break;
          case 'list_dir':
            result = await executeListDir(step.parameters);
            break;
          case 'read_url_content':
            result = await executeReadUrlContent(step.parameters);
            break;
          case 'search_web':
            result = await executeSearchWeb(step.parameters);
            break;
          case 'browser_preview':
            result = await executeBrowserPreview(step.parameters);
            break;
          case 'deploy_web_app':
            result = await executeDeployWebApp(step.parameters);
            break;
          case 'check_deploy_status':
            result = await executeCheckDeployStatus(step.parameters);
            break;
          case 'create_memory':
            result = await executeCreateMemory(step.parameters);
            break;
          case 'find_by_name':
            result = await executeFindByName(step.parameters);
            break;
          case 'view_file':
            result = await executeViewFile(step.parameters);
            break;
          case 'write_to_file':
            result = await executeWriteToFile(step.parameters);
            break;
          case 'codebase_search':
            result = await executeCodebaseSearch(step.parameters);
            break;
          case 'command_status':
            result = await executeCommandStatus(step.parameters);
            break;
          case 'read_deployment_config':
            result = await executeReadDeploymentConfig(step.parameters);
            break;
          case 'view_code_item':
            result = await executeViewCodeItem(step.parameters);
            break;
          case 'view_web_document_content_chunk':
            result = await executeViewWebDocumentContentChunk(step.parameters);
            break;
          case 'suggested_responses':
            result = await executeSuggestedResponses(step.parameters);
            break;
          case 'analyze_code':
            result = await executeAnalyzeCode(step.parameters);
            break;
          case 'generate_project':
            result = await executeGenerateProject(step.parameters);
            break;
          case 'install_dependencies':
            result = await executeInstallDependencies(step.parameters);
            break;
          case 'database_operation':
            result = await executeDatabaseOperation(step.parameters);
            break;
          case 'api_integration':
            result = await executeApiIntegration(step.parameters);
            break;
          case 'deploy_backend':
            result = await executeDeployBackend(step.parameters);
            break;
          case 'setup_ci_cd':
            result = await executeSetupCiCd(step.parameters);
            break;
          case 'analyze_performance':
            result = await executeAnalyzePerformance(step.parameters);
            break;
          case 'generate_documentation':
            result = await executeGenerateDocumentation(step.parameters);
            break;
          case 'natural_language_processing':
            result = await executeNaturalLanguageProcessing(step.parameters);
            break;
          default:
            result = { success: false, error: `Unknown tool: ${step.tool}` };
        }

        if (result.success) {
          spinner.succeed(`${step.tool} completed successfully`);
          console.log(chalk.cyan('Result:'), result.message || JSON.stringify(result, null, 2));
        } else {
          spinner.fail(`${step.tool} failed: ${result.error}`);
        }

        // Add tool result to history for context
        conversationManager.addMessage('assistant', `Tool ${step.tool} result: ${JSON.stringify(result)}`);
      }

      // Generate a final response
      spinner.start('Generating final response...');

      const responsePrompt = `
Based on the user's request "${input}" and the results of the tools execution, provide a final response that summarizes what was done and the outcome.
Make the response conversational and helpful. If there were any errors, suggest how to fix them.
If the user is trying to build a project, provide guidance on next steps.
`;

      // Get the conversation history
      const formattedHistory = conversationManager.getFormattedHistory();

      // If history is empty, just use the response prompt
      if (formattedHistory.length === 0) {
        const responseResult = await model.generateContent(responsePrompt);
        const response = responseResult.response.text();

        spinner.succeed('Done');
        console.log(chalk.green('\nAgent Response:'));
        console.log(response);

        // Add response to history
        conversationManager.addMessage('assistant', response);
      } else {
        // Add the response prompt as a user message at the beginning
        const chatWithPrompt = [
          { role: 'user', parts: [{ text: responsePrompt }] },
          ...formattedHistory
        ];

        try {
          // Generate response with conversation history
          const responseResult = await model.generateContent({ contents: chatWithPrompt });
          const response = responseResult.response.text();

          spinner.succeed('Done');
          console.log(chalk.green('\nAgent Response:'));
          console.log(response);

          // Add response to history
          conversationManager.addMessage('assistant', response);
        } catch (error) {
          // If the chat history approach fails, fall back to just the prompt
          console.log(chalk.yellow('Falling back to simpler response generation...'));
          const fallbackResult = await model.generateContent(responsePrompt);
          const fallbackResponse = fallbackResult.response.text();

          spinner.succeed('Done (fallback)');
          console.log(chalk.green('\nAgent Response:'));
          console.log(fallbackResponse);

          // Add fallback response to history
          conversationManager.addMessage('assistant', fallbackResponse);
        }
      }
    } catch (error) {
      spinner.fail('Error occurred');
      console.error(chalk.red('Error:'), error.message);
    }

    console.log('\n');
    rl.prompt();
  });
}

// Tool execution functions
async function executeEditFile({ targetFile, instruction, content, codeEdit, codeMarkdownLanguage, targetLintErrorIds }) {
  try {
    // Check if targetFile is defined
    if (!targetFile) {
      return {
        success: false,
        error: 'Target file path is required but was not provided'
      };
    }

    // If file doesn't exist and content is provided, create it
    if (!fs.existsSync(targetFile) && content) {
      const dirPath = path.dirname(targetFile);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }
      fs.writeFileSync(targetFile, content, 'utf8');
      return {
        success: true,
        message: `File ${targetFile} created successfully`
      };
    }

    // If file doesn't exist and no content but there's an instruction, generate content
    if (!fs.existsSync(targetFile) && !content && instruction) {
      const dirPath = path.dirname(targetFile);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }

      // Generate content using AI based on the instruction
      const createPrompt = `
You are an AI code generator. Create a new file according to these instructions:
${instruction}

The file path is: ${targetFile}
${codeMarkdownLanguage ? `The language is: ${codeMarkdownLanguage}` : ''}

Respond with ONLY the file content, without any explanations or markdown formatting.
`;

      try {
        const result = await model.generateContent(createPrompt);
        const generatedContent = result.response.text();

        if (!generatedContent || generatedContent.trim() === '') {
          throw new Error('Generated content is empty');
        }

        fs.writeFileSync(targetFile, generatedContent, 'utf8');
        return {
          success: true,
          message: `File ${targetFile} created successfully based on instructions`
        };
      } catch (error) {
        return {
          success: false,
          error: `Failed to generate content: ${error.message}`
        };
      }
    }

    // If file exists, edit it
    if (fs.existsSync(targetFile)) {
      const currentContent = fs.readFileSync(targetFile, 'utf8');

      if (!instruction && !codeEdit) {
        return {
          success: false,
          error: 'Instruction or codeEdit is required for editing an existing file'
        };
      }

      // If specific code edit is provided
      if (codeEdit) {
        // Handle specific code edits
        // This could be replacing specific lines, adding imports, etc.
        let editedContent = currentContent;

        if (codeEdit.replace) {
          const { pattern, replacement } = codeEdit.replace;
          editedContent = currentContent.replace(new RegExp(pattern, 'g'), replacement);
        } else if (codeEdit.addImport) {
          // Add import statement at the top of the file
          const importStatement = codeEdit.addImport;
          if (!currentContent.includes(importStatement)) {
            editedContent = importStatement + '\n' + currentContent;
          }
        } else if (codeEdit.insertAt) {
          // Insert content at specific line
          const { line, content } = codeEdit.insertAt;
          const lines = currentContent.split('\n');
          lines.splice(line - 1, 0, content);
          editedContent = lines.join('\n');
        }

        fs.writeFileSync(targetFile, editedContent, 'utf8');
        return {
          success: true,
          message: `File ${targetFile} edited with specific code changes`
        };
      }

      // If lint error IDs are provided, focus on fixing those
      let lintContext = '';
      if (targetLintErrorIds && targetLintErrorIds.length > 0) {
        lintContext = `
Focus on fixing these specific lint errors: ${targetLintErrorIds.join(', ')}
`;
      }

      // Generate edited content using AI
      const editPrompt = `
You are an AI code editor. Edit the following file according to these instructions:
${instruction}
${lintContext}

Here is the current file content:
\`\`\`${codeMarkdownLanguage || ''}
${currentContent}
\`\`\`

Respond with ONLY the new file content, without any explanations or markdown formatting.
`;

      try {
      const result = await model.generateContent(editPrompt);
      const editedContent = result.response.text();

        if (!editedContent || editedContent.trim() === '') {
          throw new Error('Generated content is empty');
        }

      fs.writeFileSync(targetFile, editedContent, 'utf8');
      return {
        success: true,
        message: `File ${targetFile} edited according to instructions`
      };
      } catch (error) {
        return {
          success: false,
          error: `Failed to generate edited content: ${error.message}`
        };
      }
    }

    // If file doesn't exist and no content, error
    return {
      success: false,
      error: `File ${targetFile} does not exist and no content provided for creation`
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * Execute a shell command
 * @param {Object} options - Command execution options
 * @param {string} options.commandLine - The command to execute
 * @param {string} options.cwd - Working directory for the command
 * @param {boolean} options.blocking - Whether to wait for the command to complete
 * @param {number} options.waitMsBeforeAsync - Milliseconds to wait before returning for async commands
 * @param {boolean} options.safeToAutoRun - Whether the command is safe to run automatically
 * @returns {Object} - Command execution result
 */
async function executeRunCommand({ commandLine, cwd = process.cwd(), blocking = true, waitMsBeforeAsync = 500, safeToAutoRun = true }) {
  const commandId = uuidv4();

  // Log the command being executed
  logger.debug(`Executing command: ${commandLine}`);
  logger.debug(`Working directory: ${cwd}`);
  logger.debug(`Blocking: ${blocking}`);

  try {
    if (!fs.existsSync(cwd)) {
      return { success: false, error: `Working directory does not exist: ${cwd}` };
    }

    // Adapt command for Windows if needed
    let adaptedCommand = commandLine;
    if (IS_WINDOWS) {
      // Check if it's a simple command that needs to be prefixed with powershell
      if (!commandLine.toLowerCase().startsWith('powershell') &&
          !commandLine.toLowerCase().includes('.exe') &&
          !commandLine.toLowerCase().includes('.cmd') &&
          !commandLine.toLowerCase().includes('.bat')) {

        // Escape double quotes for PowerShell
        const escapedCommand = commandLine.replace(/"/g, '`"');
        adaptedCommand = `powershell -Command "${escapedCommand}"`;
        logger.debug(`Adapted command for Windows: ${adaptedCommand}`);
      }
    }

    // Store command in running commands
    runningCommands[commandId] = {
      commandLine: adaptedCommand,
      originalCommand: commandLine,
      cwd,
      status: 'running',
      startTime: new Date().toISOString(),
      stdout: '',
      stderr: ''
    };

    if (blocking) {
      // Run command and wait for completion
      const execOptions = {
        cwd,
        maxBuffer: 10 * 1024 * 1024, // 10MB buffer for large outputs
        timeout: config.defaultTimeout || 60000 // Default timeout from config
      };

      const { stdout, stderr } = await execAsync(adaptedCommand, execOptions);

      // Update command status
      runningCommands[commandId].status = 'completed';
      runningCommands[commandId].stdout = stdout;
      runningCommands[commandId].stderr = stderr;
      runningCommands[commandId].endTime = new Date().toISOString();

      // Move to command history
      commandHistory[commandId] = { ...runningCommands[commandId] };
      delete runningCommands[commandId];

      return {
        success: true,
        commandId,
        stdout,
        stderr,
        message: `Command executed: ${commandLine}`
      };
    } else {
      // Run command asynchronously
      if (waitMsBeforeAsync > 0) {
        await new Promise(resolve => setTimeout(resolve, waitMsBeforeAsync));
      }

      // For Windows, ensure we use the right shell
      const spawnOptions = {
        cwd,
        shell: IS_WINDOWS ? 'powershell.exe' : true,
        stdio: ['ignore', 'pipe', 'pipe']
      };

      // For Windows, we need to handle the command differently
      let childProcess;
      if (IS_WINDOWS && adaptedCommand.startsWith('powershell')) {
        // Extract the actual command from the powershell wrapper
        const psCommand = adaptedCommand.replace(/^powershell -Command "(.*)"$/, '$1');
        childProcess = spawn('powershell', ['-Command', psCommand], spawnOptions);
      } else {
        childProcess = spawn(adaptedCommand, [], spawnOptions);
      }

      // Store child process reference
      runningCommands[commandId].process = childProcess;

      // Capture stdout and stderr
      childProcess.stdout.on('data', (data) => {
        const output = data.toString();
        runningCommands[commandId].stdout += output;
        if (config.debug) {
          logger.debug(`[${commandId}] stdout: ${output}`);
        }
      });

      childProcess.stderr.on('data', (data) => {
        const output = data.toString();
        runningCommands[commandId].stderr += output;
        if (config.debug) {
          logger.debug(`[${commandId}] stderr: ${output}`);
        }
      });

      // Handle process completion
      childProcess.on('close', (code) => {
        runningCommands[commandId].status = code === 0 ? 'completed' : 'failed';
        runningCommands[commandId].exitCode = code;
        runningCommands[commandId].endTime = new Date().toISOString();

        logger.debug(`Command ${commandId} completed with exit code ${code}`);

        // Move to command history
        commandHistory[commandId] = { ...runningCommands[commandId] };
        delete runningCommands[commandId];
      });

      return {
        success: true,
        commandId,
        message: `Command started asynchronously: ${commandLine}`,
        async: true
      };
    }
  } catch (error) {
    // Handle any errors that occurred during command execution
    logger.error(`Command execution error: ${error.message}`);

    if (runningCommands[commandId]) {
      runningCommands[commandId].status = 'failed';
      runningCommands[commandId].error = error.message;
      runningCommands[commandId].endTime = new Date().toISOString();

      // Move to command history
      commandHistory[commandId] = { ...runningCommands[commandId] };
      delete runningCommands[commandId];
    }

    return {
      success: false,
      commandId,
      error: error.message
    };
  }
}

/**
 * Execute the command status tool
 * @param {Object} parameters - Parameters for checking command status
 * @returns {Object} - Command status information
 */
async function executeCommandStatus(parameters) {
  const { commandId } = parameters;

  if (!commandId) {
    return { success: false, error: 'Command ID is required' };
  }

  // Check if command is still running
  if (runningCommands[commandId]) {
      return {
      success: true,
      status: runningCommands[commandId].status,
      stdout: runningCommands[commandId].stdout,
      stderr: runningCommands[commandId].stderr,
      startTime: runningCommands[commandId].startTime,
      running: true
    };
  }

  // Check if command is in history
  if (commandHistory[commandId]) {
    return {
      success: true,
      status: commandHistory[commandId].status,
      stdout: commandHistory[commandId].stdout,
      stderr: commandHistory[commandId].stderr,
      startTime: commandHistory[commandId].startTime,
      endTime: commandHistory[commandId].endTime,
      exitCode: commandHistory[commandId].exitCode,
      running: false
    };
  }

  return { success: false, error: `Command with ID ${commandId} not found` };
}

/**
 * Execute the containerize app tool
 * @param {Object} parameters - Parameters for containerizing the application
 * @returns {Object} - Result of containerization
 */
async function executeContainerizeApp(parameters) {
  const { appPath, dockerfilePath, imageName, buildArgs } = parameters;

  if (!appPath) {
    return { success: false, error: 'Application path is required' };
  }

  if (!imageName) {
    return { success: false, error: 'Image name is required' };
    }

    try {
    // Check if app path exists
    if (!fs.existsSync(appPath)) {
      return { success: false, error: `Application path does not exist: ${appPath}` };
    }

    // If Dockerfile is provided, check if it exists
    if (dockerfilePath && !fs.existsSync(dockerfilePath)) {
      return { success: false, error: `Dockerfile does not exist: ${dockerfilePath}` };
    }

    // Use provided Dockerfile or create one if not provided
    let dockerfile = dockerfilePath;
    if (!dockerfile) {
      dockerfile = path.join(appPath, 'Dockerfile');

      // Create a basic Dockerfile if it doesn't exist
      if (!fs.existsSync(dockerfile)) {
        const basicDockerfile = `FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "start"]`;

        fs.writeFileSync(dockerfile, basicDockerfile);
        console.log(chalk.green('Created a basic Dockerfile'));
      }
    }

    // Build the Docker image
    const buildArgsString = buildArgs ? Object.entries(buildArgs)
      .map(([key, value]) => `--build-arg ${key}=${value}`)
      .join(' ') : '';

    const buildCommand = `docker build ${buildArgsString} -t ${imageName} -f ${dockerfile} ${appPath}`;

    const result = await executeRunCommand({
      commandLine: buildCommand,
      cwd: process.cwd(),
      blocking: true
    });

    if (result.success) {
      return {
        success: true,
        imageName,
        message: `Successfully built Docker image: ${imageName}`,
        dockerfilePath: dockerfile,
        buildOutput: result.stdout
      };
    } else {
        return {
        success: false,
        error: `Failed to build Docker image: ${result.error}`,
        stderr: result.stderr
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error containerizing app: ${error.message}`
    };
  }
}

/**
 * Execute the run container tool
 * @param {Object} parameters - Parameters for running the container
 * @returns {Object} - Result of container execution
 */
async function executeRunContainer(parameters) {
  const { imageName, containerName, ports, envVars, volumes, detached } = parameters;

  if (!imageName) {
    return { success: false, error: 'Image name is required' };
  }

  try {
    // Build the docker run command
    let runCommand = 'docker run';

    // Add container name if provided
    if (containerName) {
      runCommand += ` --name ${containerName}`;
    }

    // Add port mappings if provided
    if (ports && Array.isArray(ports)) {
      ports.forEach(port => {
        runCommand += ` -p ${port}`;
      });
    }

    // Add environment variables if provided
    if (envVars && typeof envVars === 'object') {
      Object.entries(envVars).forEach(([key, value]) => {
        runCommand += ` -e ${key}=${value}`;
      });
    }

    // Add volume mappings if provided
    if (volumes && Array.isArray(volumes)) {
      volumes.forEach(volume => {
        runCommand += ` -v ${volume}`;
      });
    }
    // Add detached flag if specified
    if (detached) {
      runCommand += ' -d';
    }

    // Add the image name
    runCommand += ` ${imageName}`;

    // Execute the command
    const result = await executeRunCommand({
      commandLine: runCommand,
      cwd: process.cwd(),
      blocking: !detached
    });

    if (result.success) {
      return {
        success: true,
        imageName,
        containerName: containerName || 'unnamed',
        message: `Successfully started container from image: ${imageName}`,
        commandOutput: result.stdout,
        detached: !!detached,
        commandId: result.commandId
      };
    } else {
      return {
        success: false,
        error: `Failed to run container: ${result.error}`,
        stderr: result.stderr
      };
    }
  } catch (error) {
      return {
        success: false,
      error: `Error running container: ${error.message}`
    };
  }
}

/**
 * Execute the list containers tool
 * @param {Object} parameters - Parameters for listing containers
 * @returns {Object} - List of containers
 */
async function executeListContainers(parameters) {
  const { all } = parameters || {};

  try {
    const command = all ? 'docker ps -a' : 'docker ps';

    const result = await executeRunCommand({
      commandLine: command,
      cwd: process.cwd(),
      blocking: true
    });

    if (result.success) {
      // Parse the output to extract container information
      const lines = result.stdout.trim().split('\n');

      if (lines.length <= 1) {
      return {
          success: true,
          containers: [],
          message: 'No containers found'
        };
      }

      // Skip the header line and parse container info
      const containers = lines.slice(1).map(line => {
        const parts = line.trim().split(/\s+/);
        const containerId = parts[0];
        const image = parts[1];
        const status = parts.slice(4, -1).join(' ');
        const name = parts[parts.length - 1];

        return { containerId, image, status, name };
    });

    return {
      success: true,
        containers,
        count: containers.length,
        message: `Found ${containers.length} container(s)`
      };
    } else {
      return {
        success: false,
        error: `Failed to list containers: ${result.error}`,
        stderr: result.stderr
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error listing containers: ${error.message}`
    };
  }
}

/**
 * Execute the stop container tool
 * @param {Object} parameters - Parameters for stopping the container
 * @returns {Object} - Result of container stop operation
 */
async function executeStopContainer(parameters) {
  const { containerId, containerName } = parameters;

  if (!containerId && !containerName) {
    return { success: false, error: 'Container ID or name is required' };
  }

  try {
    const target = containerId || containerName;
    const command = `docker stop ${target}`;

    const result = await executeRunCommand({
      commandLine: command,
      cwd: process.cwd(),
      blocking: true
    });

    if (result.success) {
    return {
      success: true,
        containerId: target,
        message: `Successfully stopped container: ${target}`,
        output: result.stdout.trim()
      };
    } else {
      return {
        success: false,
        error: `Failed to stop container: ${result.error}`,
        stderr: result.stderr
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error stopping container: ${error.message}`
    };
  }
}

/**
 * Execute the remove container tool
 * @param {Object} parameters - Parameters for removing the container
 * @returns {Object} - Result of container removal operation
 */
async function executeRemoveContainer(parameters) {
  const { containerId, containerName, force } = parameters;

  if (!containerId && !containerName) {
    return { success: false, error: 'Container ID or name is required' };
  }

  try {
    const target = containerId || containerName;
    let command = `docker rm ${target}`;

    if (force) {
      command += ' -f';
    }

    const result = await executeRunCommand({
      commandLine: command,
      cwd: process.cwd(),
      blocking: true
    });

    if (result.success) {
      return {
        success: true,
        containerId: target,
        message: `Successfully removed container: ${target}`,
        output: result.stdout.trim()
      };
    } else {
      return {
        success: false,
        error: `Failed to remove container: ${result.error}`,
        stderr: result.stderr
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error removing container: ${error.message}`
    };
  }
}

/**
 * Execute the list images tool
 * @returns {Object} - List of Docker images
 */
async function executeListImages() {
  try {
    const command = 'docker images';

    const result = await executeRunCommand({
      commandLine: command,
      cwd: process.cwd(),
      blocking: true
    });

    if (result.success) {
      // Parse the output to extract image information
      const lines = result.stdout.trim().split('\n');

      if (lines.length <= 1) {
        return {
          success: true,
          images: [],
          message: 'No images found'
        };
      }

      // Skip the header line and parse image info
      const images = lines.slice(1).map(line => {
        const parts = line.trim().split(/\s+/);
        const repository = parts[0];
        const tag = parts[1];
        const imageId = parts[2];
        const created = parts[3] + ' ' + parts[4];
        const size = parts[parts.length - 1];

        return { repository, tag, imageId, created, size };
      });

      return {
        success: true,
        images,
        count: images.length,
        message: `Found ${images.length} image(s)`
      };
    } else {
      return {
        success: false,
        error: `Failed to list images: ${result.error}`,
        stderr: result.stderr
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error listing images: ${error.message}`
    };
  }
}

/**
 * Execute the remove image tool
 * @param {Object} parameters - Parameters for removing the image
 * @returns {Object} - Result of image removal operation
 */
async function executeRemoveImage(parameters) {
  const { imageId, imageName, force } = parameters;

  if (!imageId && !imageName) {
    return { success: false, error: 'Image ID or name is required' };
  }

  try {
    const target = imageId || imageName;
    let command = `docker rmi ${target}`;

    if (force) {
      command += ' -f';
    }

    const result = await executeRunCommand({
      commandLine: command,
      cwd: process.cwd(),
      blocking: true
    });

    if (result.success) {
      return {
        success: true,
        imageId: target,
        message: `Successfully removed image: ${target}`,
        output: result.stdout.trim()
      };
    } else {
      return {
        success: false,
        error: `Failed to remove image: ${result.error}`,
        stderr: result.stderr
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error removing image: ${error.message}`
    };
  }
}

async function executeGrepSearch({ searchPath, query, includes = [], caseInsensitive = false, matchPerLine }) {
  try {
    if (!searchPath) {
      return { success: false, error: 'Search path is required' };
    }

    if (!query) {
      return { success: false, error: 'Search query is required' };
    }

    // Check if search path exists
    if (!fs.existsSync(searchPath)) {
      return { success: false, error: `Search path does not exist: ${searchPath}` };
    }

    // Build grep command
    let command = `grep -r "${query}" ${searchPath}`;

    // Add case insensitive flag if specified
    if (caseInsensitive) {
      command += ' -i';
    }

    // Add includes if specified
    if (includes && includes.length > 0) {
      const includesArg = includes.map(pattern => `--include="${pattern}"`).join(' ');
      command += ` ${includesArg}`;
    }

    // Add match per line if specified
    if (matchPerLine) {
      command += ` -m ${matchPerLine}`;
    }

    try {
      const { stdout } = await execAsync(command);

      // Parse results
      const results = stdout.trim().split('\n').filter(Boolean);

      return {
        success: true,
        message: `Found ${results.length} matches for "${query}"`,
        results
      };
    } catch (error) {
      // grep returns exit code 1 if no matches found, which is not an error for us
      if (error.code === 1) {
        return {
          success: true,
          message: `No matches found for "${query}"`,
          results: []
        };
      }

      throw error;
    }
  } catch (error) {
    return {
      success: false,
      error: `Error during grep search: ${error.message}`
    };
  }
}

async function executeListDir({ directoryPath }) {
  try {
    if (!directoryPath) {
      return { success: false, error: 'Directory path is required' };
    }

    // Check if directory exists
    if (!fs.existsSync(directoryPath)) {
      return { success: false, error: `Directory does not exist: ${directoryPath}` };
    }

    // Check if it's actually a directory
    const stats = fs.statSync(directoryPath);
    if (!stats.isDirectory()) {
      return { success: false, error: `Not a directory: ${directoryPath}` };
    }

    // Read directory contents
    const contents = fs.readdirSync(directoryPath);

    // Get detailed information for each item
    const items = contents.map(item => {
      const itemPath = path.join(directoryPath, item);
      const itemStats = fs.statSync(itemPath);

      return {
        name: item,
        path: itemPath,
        isDirectory: itemStats.isDirectory(),
        size: itemStats.size,
        modified: itemStats.mtime.toISOString()
      };
    });

    // Separate directories and files
    const directories = items.filter(item => item.isDirectory);
    const files = items.filter(item => !item.isDirectory);

    return {
      success: true,
      message: `Listed ${items.length} items in ${directoryPath}`,
      items,
      directories,
      files,
      directoryPath
    };
  } catch (error) {
    return {
      success: false,
      error: `Error listing directory: ${error.message}`
    };
  }
}

async function executeReadUrlContent({ url }) {
  try {
    if (!url) {
      return { success: false, error: 'URL is required' };
    }

    // Validate URL
    try {
      new URL(url);
    } catch (e) {
      return { success: false, error: `Invalid URL: ${url}` };
    }

    // Fetch content
    const response = await axios.get(url);

    // Check if content is HTML
    const contentType = response.headers['content-type'] || '';
    const isHtml = contentType.includes('text/html');

    if (isHtml) {
      // Parse HTML content for better readability
      const $ = cheerio.load(response.data);

      // Remove scripts and styles for cleaner content
      $('script').remove();
      $('style').remove();

      // Get page title
      const title = $('title').text();

      // Get main content (trying to find the main content area)
      let mainContent = '';
      const mainElement = $('main').html() || $('article').html() || $('body').html();

      if (mainElement) {
        mainContent = cheerio.load(mainElement).text().trim();
      } else {
        mainContent = $('body').text().trim();
      }

      // Clean up the text
      mainContent = mainContent
        .replace(/\s+/g, ' ')
        .replace(/\n+/g, '\n')
        .trim();

      return {
        success: true,
        message: `Successfully fetched content from ${url}`,
        title,
        content: mainContent,
        url,
        isHtml: true
      };
    } else {
      // Handle non-HTML content
      let content = '';

      if (typeof response.data === 'string') {
        content = response.data;
      } else {
        content = JSON.stringify(response.data, null, 2);
      }

      return {
        success: true,
        message: `Successfully fetched content from ${url}`,
        content,
        contentType,
        url,
        isHtml: false
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error fetching URL content: ${error.message}`
    };
  }
}

async function executeSearchWeb({ query, domain }) {
  try {
    if (!query) {
      return { success: false, error: 'Search query is required' };
    }

    spinner.start('Searching the web...');

    // Launch browser in headless mode
    const browser = await puppeteer.launch({ headless: 'new' });
    const page = await browser.newPage();

    // Build search URL
    let searchUrl = `https://www.google.com/search?q=${encodeURIComponent(query)}`;

    // Add site: operator if domain is specified
    if (domain) {
      searchUrl = `https://www.google.com/search?q=site:${domain}+${encodeURIComponent(query)}`;
    }

    // Navigate to search page
    await page.goto(searchUrl, { waitUntil: 'domcontentloaded' });

    // Extract search results
    const results = await page.evaluate(() => {
      const searchResults = [];

      // Get all search result elements
      const resultElements = document.querySelectorAll('div.g');

      for (const element of resultElements) {
        const titleElement = element.querySelector('h3');
        if (!titleElement) continue;

        const linkElement = element.querySelector('a');
        const snippetElement = element.querySelector('div.VwiC3b');

        if (titleElement && linkElement) {
          const title = titleElement.textContent.trim();
          const url = linkElement.href;
          const snippet = snippetElement ? snippetElement.textContent.trim() : '';

          searchResults.push({ title, url, snippet });

          // Limit to 10 results
          if (searchResults.length >= 10) break;
        }
      }

      return searchResults;
    });

    // Close browser
    await browser.close();

    return {
      success: true,
      message: `Found ${results.length} search results for "${query}"`,
      results,
      query,
      domain: domain || null
    };
  } catch (error) {
    return {
      success: false,
      error: `Error searching the web: ${error.message}`
    };
  }
}

async function executeBrowserPreview({ url, name }) {
  try {
    if (!url) {
      return { success: false, error: 'URL is required' };
    }

    // Validate URL (check if it's a valid URL or a local file)
    let finalUrl = url;

    // If it's a local file path, convert to file:// URL
    if (fs.existsSync(url) && !url.startsWith('http')) {
      finalUrl = `file://${path.resolve(url)}`;
    } else if (!url.startsWith('http') && !url.startsWith('file://')) {
      // If it's not a URL or local file, check if it's a directory containing index.html
      const potentialIndexPath = path.join(url, 'index.html');
      if (fs.existsSync(potentialIndexPath)) {
        finalUrl = `file://${path.resolve(potentialIndexPath)}`;
      } else {
        // If not a valid URL format, assume it's a local path and check if it exists
        return { success: false, error: `Invalid URL or file not found: ${url}` };
      }
    }

    // For local file URLs, start a local server if it's an HTML file
    let server = null;
    let localUrl = finalUrl;

    if (finalUrl.startsWith('file://')) {
      const filePath = finalUrl.replace('file://', '');

      // Check if the path is a directory or HTML file
      if (fs.existsSync(filePath)) {
        // Get the directory containing the file
        const dirPath = fs.statSync(filePath).isDirectory() ? filePath : path.dirname(filePath);

        // Start a local server
        server = createServer({
          root: dirPath,
          cache: -1,
          cors: true,
          port: 0 // Use any available port
        });

        // Wait for the server to start
        await new Promise(resolve => {
          server.listen(0, () => {
            const port = server.server.address().port;

            // If filePath is a directory, use index.html, otherwise use the filename
            const fileName = fs.statSync(filePath).isDirectory() ? 'index.html' : path.basename(filePath);

            localUrl = `http://localhost:${port}/${fileName}`;
            resolve();
          });
        });
      }
    }

    // Open the URL in the default browser
    const openFn = await importOpen();
    await openFn(localUrl);

    return {
      success: true,
      message: `Opened ${name || 'preview'} in browser`,
      url: localUrl,
      originalUrl: url,
      name: name || 'Browser Preview',
      isLocalServer: !!server
    };
  } catch (error) {
    return {
      success: false,
      error: `Error opening browser preview: ${error.message}`
    };
  }
}

async function executeDeployWebApp({ framework, projectPath, subdomain, projectId }) {
  try {
    if (!projectPath) {
      return { success: false, error: 'Project path is required' };
    }

    if (!fs.existsSync(projectPath)) {
      return { success: false, error: `Project path does not exist: ${projectPath}` };
    }

    // Generate project ID if not provided
    const deploymentId = projectId || `proj-${uuidv4().substring(0, 8)}`;

    // Generate subdomain if not provided
    const deploymentSubdomain = subdomain || `${path.basename(projectPath)}-${uuidv4().substring(0, 6)}`;

    // Create a deployment record
    deployments[deploymentId] = {
      id: deploymentId,
      framework: framework || 'generic',
      projectPath,
      subdomain: deploymentSubdomain,
      status: 'preparing',
      createdAt: new Date().toISOString(),
      logs: []
    };

    // Log deployment info
    deployments[deploymentId].logs.push(`Preparing deployment for ${framework || 'generic'} project at ${projectPath}`);

    // Save deployment config
    fs.writeFileSync(config.deploymentConfigPath, JSON.stringify(deployments, null, 2), 'utf8');

    // Simulate deployment process (in a real implementation, this would call an actual deployment service)
    setTimeout(() => {
      deployments[deploymentId].status = 'deploying';
      deployments[deploymentId].logs.push(`Deploying project...`);

      // Update deployment config
      fs.writeFileSync(config.deploymentConfigPath, JSON.stringify(deployments, null, 2), 'utf8');

      // Simulate deployment completion after some time
      setTimeout(() => {
        deployments[deploymentId].status = 'deployed';
        deployments[deploymentId].deployedAt = new Date().toISOString();
        deployments[deploymentId].url = `https://${deploymentSubdomain}.example.com`;
        deployments[deploymentId].logs.push(`Deployment complete. Site available at ${deployments[deploymentId].url}`);

        // Update deployment config
        fs.writeFileSync(config.deploymentConfigPath, JSON.stringify(deployments, null, 2), 'utf8');
      }, 5000);
    }, 2000);

    return {
      success: true,
      message: `Deployment started for ${framework || 'generic'} project`,
      deploymentId,
      subdomain: deploymentSubdomain,
      status: 'preparing'
    };
  } catch (error) {
    return {
      success: false,
      error: `Error deploying web app: ${error.message}`
    };
  }
}

async function executeCheckDeployStatus({ deploymentId }) {
  try {
    if (!deploymentId) {
      return { success: false, error: 'Deployment ID is required' };
    }

    // Check if deployment exists
    if (!deployments[deploymentId]) {
      return { success: false, error: `Deployment with ID ${deploymentId} not found` };
    }

    const deployment = deployments[deploymentId];

    return {
      success: true,
      message: `Deployment status: ${deployment.status}`,
      deployment: {
        id: deployment.id,
        framework: deployment.framework,
        projectPath: deployment.projectPath,
        subdomain: deployment.subdomain,
        status: deployment.status,
        createdAt: deployment.createdAt,
        deployedAt: deployment.deployedAt,
        url: deployment.url,
        logs: deployment.logs
      }
    };
  } catch (error) {
    return {
      success: false,
      error: `Error checking deployment status: ${error.message}`
    };
  }
}

async function executeCreateMemory({ id, title, content, tags = [], corpusNames = [], action = 'create', userTriggered = true }) {
  try {
    if (!id) {
      return { success: false, error: 'Memory ID is required' };
    }

    if (action === 'create' || action === 'update') {
      if (!title) {
        return { success: false, error: 'Memory title is required' };
      }

      if (!content) {
        return { success: false, error: 'Memory content is required' };
      }

      // Create or update memory
      memories[id] = {
        id,
        title,
        content,
        tags: Array.isArray(tags) ? tags : tags ? [tags] : [],
        corpusNames: Array.isArray(corpusNames) ? corpusNames : corpusNames ? [corpusNames] : [],
        createdAt: memories[id]?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userTriggered: !!userTriggered
      };

      // Save memory to file
      const memoryFilePath = path.join(config.memoriesPath, `${id}.json`);
      fs.writeFileSync(memoryFilePath, JSON.stringify(memories[id], null, 2), 'utf8');

      return {
        success: true,
        message: action === 'create' ? `Memory "${title}" created` : `Memory "${title}" updated`,
        memory: memories[id]
      };
    } else if (action === 'delete') {
      // Check if memory exists
      if (!memories[id]) {
        return { success: false, error: `Memory with ID ${id} not found` };
      }

      // Get memory title before deletion
      const title = memories[id].title;

      // Delete memory
      delete memories[id];

      // Delete memory file
      const memoryFilePath = path.join(config.memoriesPath, `${id}.json`);
      if (fs.existsSync(memoryFilePath)) {
        fs.unlinkSync(memoryFilePath);
      }

      return {
        success: true,
        message: `Memory "${title}" deleted`,
        id
      };
    } else {
      return { success: false, error: `Invalid action: ${action}. Must be 'create', 'update', or 'delete'` };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error managing memory: ${error.message}`
    };
  }
}

async function executeFindByName({ searchDirectory, pattern, excludes = [], type = 'all', maxDepth = -1, extensions = [], fullPath = true }) {
  try {
    if (!searchDirectory) {
      return { success: false, error: 'Search directory is required' };
    }

    if (!pattern) {
      return { success: false, error: 'Search pattern is required' };
    }

    // Check if search directory exists
    if (!fs.existsSync(searchDirectory)) {
      return { success: false, error: `Search directory does not exist: ${searchDirectory}` };
    }

    // Convert pattern to regex
    const patternRegex = new RegExp(pattern.replace(/\*/g, '.*'), 'i');

    // Convert excludes to regex
    const excludeRegexes = excludes.map(exclude =>
      new RegExp(exclude.replace(/\*/g, '.*'), 'i')
    );

    // Convert extensions to lowercase array
    const extensionsList = Array.isArray(extensions)
      ? extensions.map(ext => ext.toLowerCase().replace(/^\./, ''))
      : [];

    // Recursive function to find files
    const findItems = (directory, currentDepth = 0) => {
      // Check max depth
      if (maxDepth > 0 && currentDepth > maxDepth) {
        return [];
      }

      const items = [];
      const entries = fs.readdirSync(directory, { withFileTypes: true });

      for (const entry of entries) {
        const entryPath = path.join(directory, entry.name);

        // Check if path is excluded
        if (excludeRegexes.some(regex => regex.test(entryPath))) {
          continue;
        }

        // Check if name matches pattern
        const nameMatches = patternRegex.test(entry.name);

        // Check file type
        const isDir = entry.isDirectory();
        const typeMatches =
          type === 'all' ||
          (type === 'file' && !isDir) ||
          (type === 'directory' && isDir);

        // Check extension if specified
        let extensionMatches = true;
        if (!isDir && extensionsList.length > 0) {
          const fileExtension = path.extname(entry.name).replace(/^\./, '').toLowerCase();
          extensionMatches = extensionsList.includes(fileExtension);
        }

        // Add item if it matches criteria
        if (nameMatches && typeMatches && extensionMatches) {
          items.push({
            name: entry.name,
            path: fullPath ? entryPath : entry.name,
            type: isDir ? 'directory' : 'file',
            extension: isDir ? null : path.extname(entry.name).replace(/^\./, '')
          });
        }

        // Recursively search subdirectories
        if (isDir) {
          const subItems = findItems(entryPath, currentDepth + 1);
          items.push(...subItems);
        }
      }

      return items;
    };

    // Start search
    const results = findItems(searchDirectory);

    return {
      success: true,
      message: `Found ${results.length} items matching "${pattern}"`,
      results,
      pattern,
      searchDirectory
    };
  } catch (error) {
    return {
      success: false,
      error: `Error finding items: ${error.message}`
    };
  }
}

async function executeViewFile({ absolutePath, startLine = 1, endLine, includeSummaryOfOtherLines = true }) {
  try {
    if (!absolutePath) {
      return { success: false, error: 'File path is required' };
    }

    // Check if file exists
    if (!fs.existsSync(absolutePath)) {
      return { success: false, error: `File does not exist: ${absolutePath}` };
    }

    // Check if it's a file, not a directory
    const stats = fs.statSync(absolutePath);
    if (!stats.isFile()) {
      return { success: false, error: `Not a file: ${absolutePath}` };
    }

    // Read file content
    const content = fs.readFileSync(absolutePath, 'utf8');
    const lines = content.split('\n');

    // Adjust line numbers
    startLine = Math.max(1, startLine);
    endLine = endLine ? Math.min(lines.length, endLine) : lines.length;

    // Get requested lines
    const requestedLines = lines.slice(startLine - 1, endLine);

    // Prepare result
    const result = {
      success: true,
      message: `Showing lines ${startLine} to ${endLine} of ${lines.length}`,
      filename: path.basename(absolutePath),
      path: absolutePath,
      totalLines: lines.length,
      startLine,
      endLine,
      content: requestedLines.join('\n')
    };

    // Include summary if requested
    if (includeSummaryOfOtherLines) {
      if (startLine > 1) {
        result.beforeSummary = `[Lines 1-${startLine - 1} omitted]`;
      }

      if (endLine < lines.length) {
        result.afterSummary = `[Lines ${endLine + 1}-${lines.length} omitted]`;
      }
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: `Error viewing file: ${error.message}`
    };
  }
}

async function executeWriteToFile({ targetFile, codeContent, emptyFile = false }) {
  try {
    if (!targetFile) {
      return { success: false, error: 'Target file path is required' };
    }

    // Create directories if they don't exist
    const dirPath = path.dirname(targetFile);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }

    // Write content to file
    if (emptyFile) {
      // Create an empty file
      fs.writeFileSync(targetFile, '', 'utf8');
    } else if (codeContent) {
      // Write the provided content
      fs.writeFileSync(targetFile, codeContent, 'utf8');
    } else {
      return { success: false, error: 'File content is required' };
    }

    return {
      success: true,
      message: `File ${targetFile} written successfully`,
      targetFile,
      size: emptyFile ? 0 : codeContent.length
    };
  } catch (error) {
    return {
      success: false,
      error: `Error writing to file: ${error.message}`
    };
  }
}

async function executeCodebaseSearch({ query, targetDirectories = ['.'] }) {
  try {
    if (!query) {
      return { success: false, error: 'Search query is required' };
    }

    // Validate target directories
    const validDirectories = targetDirectories.filter(dir => fs.existsSync(dir));

    if (validDirectories.length === 0) {
      return { success: false, error: 'No valid target directories found' };
    }

    // Build search command
    const searchDirs = validDirectories.join(' ');
    const command = `grep -r -l "${query}" ${searchDirs}`;

    try {
      // Find matching files
      const { stdout } = await execAsync(command);
      const matchingFiles = stdout.trim().split('\n').filter(Boolean);

      // Get context for each match
      const results = [];

      for (const file of matchingFiles) {
        // Get line numbers of matches
        const { stdout: matchLines } = await execAsync(`grep -n "${query}" "${file}"`);

        const fileMatches = matchLines.trim().split('\n').map(line => {
          // Parse line number and content
          const match = line.match(/^(\d+):(.*)/);
          if (match) {
            const lineNumber = parseInt(match[1]);
            const lineContent = match[2];

            // Read surrounding lines for context
            const fileContent = fs.readFileSync(file, 'utf8').split('\n');
            const contextStart = Math.max(0, lineNumber - 3);
            const contextEnd = Math.min(fileContent.length - 1, lineNumber + 2);
            const context = fileContent.slice(contextStart, contextEnd + 1);

            return {
              file,
              lineNumber,
              lineContent,
              context,
              contextStart: contextStart + 1, // 1-indexed
              contextEnd: contextEnd + 1      // 1-indexed
            };
          }
          return null;
        }).filter(Boolean);

        results.push(...fileMatches);
      }

      return {
        success: true,
        message: `Found ${results.length} matches in ${matchingFiles.length} files`,
        results,
        query,
        targetDirectories: validDirectories
      };
    } catch (error) {
      // grep returns exit code 1 if no matches found
      if (error.code === 1) {
        return {
          success: true,
          message: `No matches found for "${query}"`,
          results: [],
          query,
          targetDirectories: validDirectories
        };
      }

      throw error;
    }
  } catch (error) {
    return {
      success: false,
      error: `Error searching codebase: ${error.message}`
    };
  }
}

async function executeReadDeploymentConfig({ projectPath }) {
  try {
    if (!projectPath) {
      return { success: false, error: 'Project path is required' };
    }

    // Check if project path exists
    if (!fs.existsSync(projectPath)) {
      return { success: false, error: `Project path does not exist: ${projectPath}` };
    }

    // Common deployment config files
    const configFiles = [
      'vercel.json',
      'netlify.toml',
      'firebase.json',
      '.github/workflows/deploy.yml',
      '.gitlab-ci.yml',
      'docker-compose.yml',
      'Dockerfile',
      '.travis.yml',
      'now.json',
      'serverless.yml',
      'appspec.yml',
      'app.yaml',
      'k8s',
      'kubernetes',
      '.circleci/config.yml'
    ];

    // Look for config files
    const foundConfigs = [];

    for (const configFile of configFiles) {
      const fullPath = path.join(projectPath, configFile);

      if (fs.existsSync(fullPath)) {
        try {
          const content = fs.readFileSync(fullPath, 'utf8');

          foundConfigs.push({
            path: fullPath,
            name: path.basename(configFile),
            content
          });
        } catch (e) {
          // Skip files that can't be read
        }
      }
    }

    // Also check for deployment scripts in package.json
    const packageJsonPath = path.join(projectPath, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

        if (packageJson.scripts) {
          const deployScripts = Object.entries(packageJson.scripts)
            .filter(([name, script]) =>
              name.includes('deploy') ||
              script.includes('deploy') ||
              script.includes('vercel') ||
              script.includes('netlify') ||
              script.includes('firebase') ||
              script.includes('heroku')
            );

          if (deployScripts.length > 0) {
            foundConfigs.push({
              path: packageJsonPath,
              name: 'package.json',
              deployScripts: Object.fromEntries(deployScripts)
            });
          }
        }
      } catch (e) {
        // Skip if package.json can't be parsed
      }
    }

    if (foundConfigs.length === 0) {
      return {
        success: true,
        message: 'No deployment configuration found',
        projectPath,
        configs: []
      };
    }

    return {
      success: true,
      message: `Found ${foundConfigs.length} deployment configurations`,
      projectPath,
      configs: foundConfigs
    };
  } catch (error) {
    return {
      success: false,
      error: `Error reading deployment config: ${error.message}`
    };
  }
}

async function executeViewCodeItem({ nodePath }) {
  try {
    if (!nodePath) {
      return { success: false, error: 'Node path is required' };
    }

    // Extract file path and item path from node path
    const parts = nodePath.split('#');
    const filePath = parts[0];
    const itemPath = parts.length > 1 ? parts[1] : null;

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return { success: false, error: `File does not exist: ${filePath}` };
    }

    // Read file content
    const content = fs.readFileSync(filePath, 'utf8');

    // If no specific item is requested, return the whole file
    if (!itemPath) {
      return {
        success: true,
        message: `Showing entire file: ${filePath}`,
        filePath,
        content
      };
    }

    // Parse the file based on extension
    const extension = path.extname(filePath).toLowerCase();

    if (['.js', '.ts', '.jsx', '.tsx'].includes(extension)) {
      // For JavaScript/TypeScript files, try to find the requested item
      // This is a simplified implementation - a real one would use an AST parser

      // Function or class definition (simplified)
      const itemRegex = new RegExp(`(function|class|const|let|var)\\s+${itemPath}\\s*[({=]`);
      const match = content.match(itemRegex);

      if (match) {
        const startIndex = match.index;

        // Find the end of the item (simplified)
        let depth = 0;
        let endIndex = startIndex;

        for (let i = startIndex; i < content.length; i++) {
          const char = content[i];

          if (char === '{' || char === '(') {
            depth++;
          } else if (char === '}' || char === ')') {
            depth--;

            if (depth <= 0) {
              endIndex = i + 1;
              break;
            }
          }
        }

        const itemContent = content.substring(startIndex, endIndex);

        return {
          success: true,
          message: `Found item ${itemPath} in ${filePath}`,
          filePath,
          itemPath,
          content: itemContent
        };
      }

      return {
        success: false,
        error: `Item ${itemPath} not found in ${filePath}`
      };
    }

    // For other file types, return the whole file
    return {
      success: true,
      message: `Showing entire file (item-specific view not supported for this file type): ${filePath}`,
      filePath,
      content
    };
  } catch (error) {
    return {
      success: false,
      error: `Error viewing code item: ${error.message}`
    };
  }
}

async function executeViewWebDocumentContentChunk({ url, position = 0 }) {
  try {
    if (!url) {
      return { success: false, error: 'URL is required' };
    }

    // Validate URL
    try {
      new URL(url);
    } catch (e) {
      return { success: false, error: `Invalid URL: ${url}` };
    }

    // Fetch content
    const response = await axios.get(url);

    // Check if content is HTML
    const contentType = response.headers['content-type'] || '';
    const isHtml = contentType.includes('text/html');

    if (isHtml) {
      // Parse HTML content
      const dom = new JSDOM(response.data);
      const document = dom.window.document;

      // Extract text content
      let textContent = '';

      // Try to find main content area
      const mainElement = document.querySelector('main') ||
                          document.querySelector('article') ||
                          document.querySelector('.content') ||
                          document.querySelector('#content') ||
                          document.body;

      if (mainElement) {
        // Remove scripts, styles, and other non-content elements
        const scriptsAndStyles = mainElement.querySelectorAll('script, style, nav, footer, header, aside, form');
        for (const element of scriptsAndStyles) {
          if (element.parentNode) {
            element.parentNode.removeChild(element);
          }
        }

        textContent = mainElement.textContent
          .replace(/\s+/g, ' ')
          .trim();
      }

      // Split content into chunks
      const chunkSize = 4000; // Characters per chunk
      const chunks = [];

      for (let i = 0; i < textContent.length; i += chunkSize) {
        chunks.push(textContent.substring(i, i + chunkSize));
      }

      // Validate position
      const validPosition = Math.min(Math.max(0, position), chunks.length - 1);

      return {
        success: true,
        message: `Showing chunk ${validPosition + 1} of ${chunks.length} from ${url}`,
        url,
        title: document.title,
        totalChunks: chunks.length,
        position: validPosition,
        content: chunks[validPosition],
        hasNextChunk: validPosition < chunks.length - 1,
        hasPreviousChunk: validPosition > 0
      };
    } else {
      // For non-HTML content, return as is
      return {
        success: true,
        message: `Content from ${url} (not HTML)`,
        url,
        content: typeof response.data === 'string' ? response.data : JSON.stringify(response.data, null, 2),
        contentType
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error viewing web document: ${error.message}`
    };
  }
}

async function executeSuggestedResponses({ suggestions }) {
  try {
    if (!suggestions || !Array.isArray(suggestions) || suggestions.length === 0) {
      return { success: false, error: 'Suggestions array is required and must not be empty' };
    }

    // Filter out invalid suggestions
    const validSuggestions = suggestions.filter(suggestion =>
      suggestion && typeof suggestion === 'string' && suggestion.trim() !== ''
    );

    if (validSuggestions.length === 0) {
      return { success: false, error: 'No valid suggestions provided' };
    }

    return {
      success: true,
      message: `Providing ${validSuggestions.length} suggested responses`,
      suggestions: validSuggestions
    };
  } catch (error) {
    return {
      success: false,
      error: `Error providing suggested responses: ${error.message}`
    };
  }
}

async function executeAnalyzeCode({ codePath, language, analysisType = 'general' }) {
  try {
    if (!codePath) {
      return { success: false, error: 'Code path is required' };
    }

    // Check if file exists
    if (!fs.existsSync(codePath)) {
      return { success: false, error: `File does not exist: ${codePath}` };
    }

    // Read file content
    const code = fs.readFileSync(codePath, 'utf8');

    // Determine language from file extension if not provided
    let codeLanguage = language;
    if (!codeLanguage) {
      const extension = path.extname(codePath).toLowerCase();
      const languageMap = {
        '.js': 'JavaScript',
        '.ts': 'TypeScript',
        '.py': 'Python',
        '.java': 'Java',
        '.c': 'C',
        '.cpp': 'C++',
        '.cs': 'C#',
        '.go': 'Go',
        '.rb': 'Ruby',
        '.php': 'PHP',
        '.swift': 'Swift',
        '.kt': 'Kotlin',
        '.rs': 'Rust',
        '.html': 'HTML',
        '.css': 'CSS',
        '.jsx': 'React JSX',
        '.tsx': 'React TSX',
        '.json': 'JSON',
        '.md': 'Markdown',
        '.yml': 'YAML',
        '.xml': 'XML',
        '.sql': 'SQL'
      };

      codeLanguage = languageMap[extension] || 'Unknown';
    }

    // Generate appropriate prompt based on analysis type
    let analysisPrompt;

    switch (analysisType) {
      case 'security':
        analysisPrompt = `
Analyze the following ${codeLanguage} code for security vulnerabilities:

\`\`\`${codeLanguage}
${code}
\`\`\`

Identify any security issues such as:
1. Injection vulnerabilities
2. Authentication problems
3. Sensitive data exposure
4. Security misconfigurations
5. Cross-site scripting (XSS)
6. Insecure dependencies
7. Insecure use of cryptography
8. Any other security concerns

For each issue:
- Describe the vulnerability
- Rate its severity (Critical, High, Medium, Low)
- Explain how it could be exploited
- Suggest a fix

Format your response as JSON with this structure:
{
  "vulnerabilities": [
    {
      "type": "vulnerability type",
      "severity": "severity level",
      "description": "detailed description",
      "location": "where in the code",
      "recommendation": "how to fix it"
    }
  ],
  "summary": "brief summary of findings",
  "overallRisk": "overall risk assessment"
}
`;
        break;

      case 'performance':
        analysisPrompt = `
Analyze the following ${codeLanguage} code for performance issues:

\`\`\`${codeLanguage}
${code}
\`\`\`

Identify any performance concerns such as:
1. Inefficient algorithms (O(n²) or worse)
2. Unnecessary computations
3. Memory leaks
4. Excessive resource usage
5. Blocking operations
6. Redundant operations
7. Any other performance bottlenecks

For each issue:
- Describe the performance problem
- Rate its impact (Critical, High, Medium, Low)
- Explain why it's inefficient
- Suggest an optimization

Format your response as JSON with this structure:
{
  "performanceIssues": [
    {
      "type": "issue type",
      "impact": "impact level",
      "description": "detailed description",
      "location": "where in the code",
      "recommendation": "how to optimize it"
    }
  ],
  "summary": "brief summary of findings",
  "overallPerformance": "overall performance assessment"
}
`;
        break;

      case 'style':
        analysisPrompt = `
Analyze the following ${codeLanguage} code for style and readability:

\`\`\`${codeLanguage}
${code}
\`\`\`

Identify any style or readability issues such as:
1. Inconsistent formatting
2. Poor naming conventions
3. Lack of comments or documentation
4. Overly complex code
5. Duplication
6. Violation of common coding standards
7. Any other readability concerns

For each issue:
- Describe the style problem
- Suggest an improvement

Format your response as JSON with this structure:
{
  "styleIssues": [
    {
      "type": "issue type",
      "description": "detailed description",
      "location": "where in the code",
      "recommendation": "how to improve it"
    }
  ],
  "summary": "brief summary of findings",
  "overallReadability": "overall readability assessment"
}
`;
        break;

      default: // general analysis
        analysisPrompt = `
Analyze the following ${codeLanguage} code comprehensively:

\`\`\`${codeLanguage}
${code}
\`\`\`

Provide a detailed analysis including:
1. Code structure and organization
2. Potential bugs or errors
3. Performance considerations
4. Security concerns
5. Style and readability
6. Best practices conformance
7. Suggestions for improvement

Format your response as JSON with this structure:
{
  "summary": "brief summary of the code",
  "issues": [
    {
      "type": "issue type (bug, performance, security, style, etc.)",
      "severity": "severity level if applicable",
      "description": "detailed description",
      "location": "where in the code",
      "recommendation": "how to address it"
    }
  ],
  "strengths": ["list of code strengths"],
  "suggestions": ["list of general improvement suggestions"],
  "overallAssessment": "overall quality assessment"
}
`;
    }

    // Call AI to analyze the code
    const analysisResult = await model.generateContent(analysisPrompt);
    const analysisText = analysisResult.response.text();

    // Extract JSON response
    let analysis;
    try {
      // Try to parse the entire response as JSON
      analysis = JSON.parse(analysisText);
    } catch (e) {
      // If that fails, try to extract JSON from text
      const jsonMatch = analysisText.match(/```json\n([\s\S]*?)\n```/) ||
                        analysisText.match(/({[\s\S]*})/);

      if (jsonMatch) {
        analysis = JSON.parse(jsonMatch[1].trim());
      } else {
        // If JSON parsing fails, return the raw analysis
        return {
          success: true,
          message: `Code analysis completed for ${path.basename(codePath)}`,
          codePath,
          language: codeLanguage,
          analysisType,
          rawAnalysis: analysisText
        };
      }
    }

    return {
      success: true,
      message: `Code analysis completed for ${path.basename(codePath)}`,
      codePath,
      language: codeLanguage,
      analysisType,
      analysis
    };
  } catch (error) {
    return {
      success: false,
      error: `Error analyzing code: ${error.message}`
    };
  }
}

async function executeGenerateProject({ projectName, framework, language, features = [] }) {
  try {
    if (!projectName) {
      return { success: false, error: 'Project name is required' };
    }

    // Sanitize project name for directory creation
    const sanitizedProjectName = projectName.replace(/[^a-zA-Z0-9-_]/g, '-').toLowerCase();
    const projectPath = path.resolve(sanitizedProjectName);

    // Check if project directory already exists
    if (fs.existsSync(projectPath)) {
      return { success: false, error: `Project directory already exists: ${projectPath}` };
    }

    // Create project directory
    fs.mkdirSync(projectPath, { recursive: true });

    // Determine language if not specified
    const projectLanguage = language || (framework ? getDefaultLanguage(framework) : 'JavaScript');

    // Generate project structure based on framework
    let projectStructure;
    let setupCommands = [];

    if (framework) {
      // Use framework-specific generator if available
      const result = await generateFrameworkProject(projectPath, framework, projectLanguage, features);

      if (!result.success) {
        // Clean up partial project
        if (fs.existsSync(projectPath)) {
          fs.rmSync(projectPath, { recursive: true, force: true });
        }

        return result;
      }

      projectStructure = result.structure;
      setupCommands = result.commands;
    } else {
      // Generate a simple project structure
      const result = await generateSimpleProject(projectPath, projectLanguage, features);

      if (!result.success) {
        // Clean up partial project
        if (fs.existsSync(projectPath)) {
          fs.rmSync(projectPath, { recursive: true, force: true });
        }

        return result;
      }

      projectStructure = result.structure;
      setupCommands = result.commands;
    }

    // Create a README file
    const readmePath = path.join(projectPath, 'README.md');
    const readmeContent = generateReadme(projectName, framework, projectLanguage, features);
    fs.writeFileSync(readmePath, readmeContent, 'utf8');

    return {
      success: true,
      message: `Project ${projectName} created successfully`,
      projectName,
      framework: framework || 'none',
      language: projectLanguage,
      features,
      projectPath,
      structure: projectStructure,
      setupCommands
    };
  } catch (error) {
    return {
      success: false,
      error: `Error generating project: ${error.message}`
    };
  }
}

// Helper function to determine default language for a framework
function getDefaultLanguage(framework) {
  const frameworkLanguageMap = {
    'react': 'JavaScript',
    'vue': 'JavaScript',
    'angular': 'TypeScript',
    'next': 'JavaScript',
    'nuxt': 'JavaScript',
    'svelte': 'JavaScript',
    'express': 'JavaScript',
    'fastapi': 'Python',
    'django': 'Python',
    'flask': 'Python',
    'spring': 'Java',
    'rails': 'Ruby',
    'laravel': 'PHP',
    'dotnet': 'C#'
  };

  return frameworkLanguageMap[framework.toLowerCase()] || 'JavaScript';
}

// Helper function to generate a simple project
async function generateSimpleProject(projectPath, language, features) {
  try {
    let structure = [];
    let commands = [];

    switch (language.toLowerCase()) {
      case 'javascript':
        // Create package.json
        const packageJson = {
          name: path.basename(projectPath),
          version: '1.0.0',
          description: 'A JavaScript project',
          main: 'index.js',
          scripts: {
            start: 'node index.js',
            test: 'echo "Error: no test specified" && exit 1'
          },
          keywords: [],
          author: '',
          license: 'ISC'
        };

        fs.writeFileSync(
          path.join(projectPath, 'package.json'),
          JSON.stringify(packageJson, null, 2),
          'utf8'
        );

        // Create main JS file
        fs.writeFileSync(
          path.join(projectPath, 'index.js'),
          'console.log("Hello, world!");',
          'utf8'
        );

        // Create a .gitignore file
        fs.writeFileSync(
          path.join(projectPath, '.gitignore'),
          'node_modules\n.env\n.DS_Store',
          'utf8'
        );

        structure = ['package.json', 'index.js', '.gitignore'];
        commands = ['npm install'];
        break;

      case 'python':
        // Create main.py
        fs.writeFileSync(
          path.join(projectPath, 'main.py'),
          'print("Hello, world!")',
          'utf8'
        );

        // Create requirements.txt
        fs.writeFileSync(
          path.join(projectPath, 'requirements.txt'),
          '# Add your dependencies here',
          'utf8'
        );

        // Create .gitignore
        fs.writeFileSync(
          path.join(projectPath, '.gitignore'),
          '__pycache__/\n*.py[cod]\n*$py.class\n.env\n.venv\nenv/\nvenv/\nENV/\nenv.bak/\nvenv.bak/\n.DS_Store',
          'utf8'
        );

        structure = ['main.py', 'requirements.txt', '.gitignore'];
        commands = ['python -m venv venv', 'source venv/bin/activate'];
        break;

      // Add more languages as needed

      default:
        return {
          success: false,
          error: `Unsupported language: ${language}`
        };
    }

    return {
      success: true,
      structure,
      commands
    };
  } catch (error) {
    return {
      success: false,
      error: `Error generating simple project: ${error.message}`
    };
  }
}

// Helper function to generate a framework-specific project
async function generateFrameworkProject(projectPath, framework, language, features) {
  try {
    // For real implementation, this could call framework-specific CLI tools
    // For now, just return instructions on how to create the project

    let commands = [];
    let structure = [];

    switch (framework.toLowerCase()) {
      case 'react':
        commands = [
          `npx create-react-app ${path.basename(projectPath)}`,
          `cd ${path.basename(projectPath)}`,
          'npm start'
        ];

        structure = [
          'public/',
          'src/',
          'package.json',
          'README.md',
          '.gitignore'
        ];
        break;

      case 'vue':
        commands = [
          `npx @vue/cli create ${path.basename(projectPath)}`,
          `cd ${path.basename(projectPath)}`,
          'npm run serve'
        ];

        structure = [
          'public/',
          'src/',
          'package.json',
          'README.md',
          '.gitignore'
        ];
        break;

      // Add more frameworks as needed

      default:
        return {
          success: false,
          error: `Unsupported framework: ${framework}`
        };
    }

    return {
      success: true,
      structure,
      commands,
      note: "This is a placeholder. In a real implementation, the project files would be created directly."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error generating framework project: ${error.message}`
    };
  }
}

// Helper function to generate a README file
function generateReadme(projectName, framework, language, features) {
  const featuresList = features.length > 0
    ? `\n\n## Features\n\n${features.map(feature => `- ${feature}`).join('\n')}`
    : '';

  const frameworkInfo = framework
    ? `\nThis project was built with ${framework}.`
    : '';

  return `# ${projectName}

A ${language} project.${frameworkInfo}${featuresList}

## Getting Started

1. Clone this repository
2. Install dependencies
3. Run the project

## License

This project is licensed under the ISC License.
`;
}

// Implement executeInstallDependencies function
async function executeInstallDependencies({ packageManager, dependencies = [], projectPath = '.', dev = false }) {
  try {
    if (!fs.existsSync(projectPath)) {
      return { success: false, error: `Project path does not exist: ${projectPath}` };
    }

    // Determine package manager if not specified
    let pm = packageManager;
    if (!pm) {
      // Auto-detect package manager based on project files
      if (fs.existsSync(path.join(projectPath, 'package.json'))) {
        if (fs.existsSync(path.join(projectPath, 'yarn.lock'))) {
          pm = 'yarn';
        } else {
          pm = 'npm';
        }
      } else if (fs.existsSync(path.join(projectPath, 'requirements.txt'))) {
        pm = 'pip';
      } else if (fs.existsSync(path.join(projectPath, 'Gemfile'))) {
        pm = 'bundle';
      } else if (fs.existsSync(path.join(projectPath, 'pom.xml'))) {
        pm = 'mvn';
      } else if (fs.existsSync(path.join(projectPath, 'build.gradle'))) {
        pm = 'gradle';
      } else {
        return { success: false, error: 'Could not determine package manager. Please specify.' };
      }
    }

    // Construct command based on package manager
    let command;
    switch (pm.toLowerCase()) {
      case 'npm':
        command = `npm install ${dev ? '--save-dev ' : ''}${dependencies.join(' ')}`;
        break;
      case 'yarn':
        command = `yarn add ${dev ? '--dev ' : ''}${dependencies.join(' ')}`;
        break;
      case 'pip':
        command = `pip install ${dependencies.join(' ')}`;
        break;
      case 'bundle':
        // For bundle, we don't directly install packages, just note that Gemfile should be updated
        return {
          success: true,
          message: 'For Ruby projects, add the gems to your Gemfile then run "bundle install"',
          dependencies,
          packageManager: pm,
          nextStep: 'Run "bundle install" after updating Gemfile'
        };
      case 'mvn':
        return {
          success: true,
          message: 'For Maven projects, add dependencies to pom.xml then run "mvn install"',
          dependencies,
          packageManager: pm,
          nextStep: 'Run "mvn install" after updating pom.xml'
        };
      case 'gradle':
        return {
          success: true,
          message: 'For Gradle projects, add dependencies to build.gradle then run "gradle build"',
          dependencies,
          packageManager: pm,
          nextStep: 'Run "gradle build" after updating build.gradle'
        };
      default:
        return { success: false, error: `Unsupported package manager: ${pm}` };
    }

    // Execute the command
    const result = await executeRunCommand({
      commandLine: command,
      cwd: projectPath,
      blocking: true
    });

    if (result.success) {
      return {
        success: true,
        message: `Successfully installed dependencies using ${pm}`,
        dependencies,
        packageManager: pm,
        projectPath,
        commandOutput: result.stdout
      };
    } else {
      return {
        success: false,
        error: `Failed to install dependencies: ${result.error}`,
        stderr: result.stderr
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error installing dependencies: ${error.message}`
    };
  }
}

// Implement executeDatabaseOperation function
async function executeDatabaseOperation({ operation, dbType, connectionString, query }) {
  try {
    if (!operation) {
      return { success: false, error: 'Database operation is required' };
    }

    if (!dbType) {
      return { success: false, error: 'Database type is required' };
    }

    // This is a simplified implementation - in a real scenario, you'd use
    // actual database connectors based on dbType

    return {
      success: true,
      message: `Database operation simulation for ${operation} on ${dbType}`,
      operation,
      dbType,
      note: "This is a placeholder. In a real implementation, actual database operations would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error executing database operation: ${error.message}`
    };
  }
}

// Implement executeApiIntegration function
async function executeApiIntegration({ apiName, endpoint, method = 'GET', headers = {}, body = null }) {
  try {
    if (!apiName) {
      return { success: false, error: 'API name is required' };
    }

    if (!endpoint) {
      return { success: false, error: 'API endpoint is required' };
    }

    // Validate method
    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
    if (!validMethods.includes(method.toUpperCase())) {
      return { success: false, error: `Invalid HTTP method: ${method}. Must be one of ${validMethods.join(', ')}` };
    }

    // Simulated API call - in a real implementation, would use axios or similar
    return {
      success: true,
      message: `API integration simulation for ${apiName} using ${method} to ${endpoint}`,
      apiName,
      endpoint,
      method,
      note: "This is a placeholder. In a real implementation, actual API calls would be made."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error integrating with API: ${error.message}`
    };
  }
}

// Implement executeDeployBackend function
async function executeDeployBackend({ servicePath, platform, region, environment }) {
  try {
    if (!servicePath) {
      return { success: false, error: 'Service path is required' };
    }

    if (!fs.existsSync(servicePath)) {
      return { success: false, error: `Service path does not exist: ${servicePath}` };
    }

    if (!platform) {
      return { success: false, error: 'Deployment platform is required' };
    }

    // Deploy based on platform (simulation)
    const deploymentId = `deploy-${uuidv4().substring(0, 8)}`;

    return {
      success: true,
      message: `Backend deployment simulation for ${platform}`,
      deploymentId,
      servicePath,
      platform,
      region: region || 'default',
      environment: environment || 'development',
      note: "This is a placeholder. In a real implementation, actual deployment would occur."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error deploying backend: ${error.message}`
    };
  }
}

// Implement executeSetupCiCd function
async function executeSetupCiCd({ provider, projectPath, steps = [] }) {
  try {
    if (!provider) {
      return { success: false, error: 'CI/CD provider is required' };
    }

    if (!projectPath) {
      return { success: false, error: 'Project path is required' };
    }

    if (!fs.existsSync(projectPath)) {
      return { success: false, error: `Project path does not exist: ${projectPath}` };
    }

    // Generate CI/CD configuration file based on provider
    let configFile;
    let configContent;

    switch (provider.toLowerCase()) {
      case 'github':
      case 'github actions':
        configFile = path.join(projectPath, '.github/workflows/ci-cd.yml');
        configContent = `name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Build
        run: echo "Building project..."
      - name: Test
        run: echo "Running tests..."
      - name: Deploy
        run: echo "Deploying project..."
`;
        break;

      case 'gitlab':
      case 'gitlab ci':
        configFile = path.join(projectPath, '.gitlab-ci.yml');
        configContent = `stages:
  - build
  - test
  - deploy

build:
  stage: build
  script:
    - echo "Building project..."

test:
  stage: test
  script:
    - echo "Running tests..."

deploy:
  stage: deploy
  script:
    - echo "Deploying project..."
  only:
    - main
`;
        break;

      case 'travis':
      case 'travis ci':
        configFile = path.join(projectPath, '.travis.yml');
        configContent = `language: node_js
node_js:
  - 14
script:
  - echo "Building project..."
  - echo "Running tests..."
deploy:
  provider: script
  script: echo "Deploying project..."
  on:
    branch: main
`;
        break;

      default:
        return { success: false, error: `Unsupported CI/CD provider: ${provider}` };
    }

    // Create directory if it doesn't exist
    const dir = path.dirname(configFile);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Write the config file
    fs.writeFileSync(configFile, configContent, 'utf8');

    return {
      success: true,
      message: `CI/CD configuration for ${provider} created at ${configFile}`,
      provider,
      configFile,
      configContent
    };
  } catch (error) {
    return {
      success: false,
      error: `Error setting up CI/CD: ${error.message}`
    };
  }
}

// Implement executeAnalyzePerformance function
async function executeAnalyzePerformance({ targetUrl, metrics = [], duration = 30 }) {
  try {
    if (!targetUrl) {
      return { success: false, error: 'Target URL is required' };
    }

    // Simple validation of URL
    try {
      new URL(targetUrl);
    } catch (e) {
      return { success: false, error: `Invalid URL: ${targetUrl}` };
    }

    // This would normally launch real performance analysis tools
    // Here we just simulate it

    return {
      success: true,
      message: `Performance analysis simulation for ${targetUrl}`,
      targetUrl,
      metrics: metrics.length > 0 ? metrics : ['loadTime', 'firstContentfulPaint', 'totalBlockingTime'],
      duration,
      results: {
        loadTime: '1.2s',
        firstContentfulPaint: '0.8s',
        totalBlockingTime: '120ms',
        performanceScore: 85
      },
      note: "This is a placeholder. In a real implementation, actual performance analysis would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error analyzing performance: ${error.message}`
    };
  }
}

// Implement executeGenerateDocumentation function
async function executeGenerateDocumentation({ sourcePath, outputFormat = 'markdown', outputPath }) {
  try {
    if (!sourcePath) {
      return { success: false, error: 'Source path is required' };
    }

    if (!fs.existsSync(sourcePath)) {
      return { success: false, error: `Source path does not exist: ${sourcePath}` };
    }

    // Determine output path if not provided
    const finalOutputPath = outputPath || path.join(sourcePath, 'docs');

    // Create output directory if it doesn't exist
    if (!fs.existsSync(finalOutputPath)) {
      fs.mkdirSync(finalOutputPath, { recursive: true });
    }

    // This would normally use documentation generation tools
    // Here we just create a simple template
    const readmeContent = `# Project Documentation

## Overview
Documentation for the codebase at ${sourcePath}.

## File Structure
\`\`\`
${sourcePath}/
├── ...
\`\`\`

## Components
_Documentation for components would be generated here._

## API Reference
_API documentation would be generated here._

## Getting Started
_Getting started guide would be generated here._
`;

    const outputFile = path.join(finalOutputPath, 'README.md');
    fs.writeFileSync(outputFile, readmeContent, 'utf8');

    return {
      success: true,
      message: `Documentation generated at ${outputFile}`,
      sourcePath,
      outputPath: finalOutputPath,
      outputFormat,
      generatedFiles: [outputFile],
      note: "This is a placeholder. In a real implementation, comprehensive documentation would be generated."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error generating documentation: ${error.message}`
    };
  }
}

// Implement executeSetupMonitoring function
async function executeSetupMonitoring({ serviceName, metrics = [], alertThresholds = {} }) {
  try {
    if (!serviceName) {
      return { success: false, error: 'Service name is required' };
    }

    // This would normally set up real monitoring tools
    // Here we just simulate it

    return {
      success: true,
      message: `Monitoring setup simulation for ${serviceName}`,
      serviceName,
      metrics: metrics.length > 0 ? metrics : ['cpu', 'memory', 'requests', 'errors'],
      alertThresholds: Object.keys(alertThresholds).length > 0 ? alertThresholds : {
        cpu: '80%',
        memory: '85%',
        errorRate: '5%'
      },
      note: "This is a placeholder. In a real implementation, actual monitoring would be configured."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error setting up monitoring: ${error.message}`
    };
  }
}

// Implement executeScrapeWebsite function
async function executeScrapeWebsite({ url, selectors = {}, pagination = null, outputFormat = 'json' }) {
  try {
    if (!url) {
      return { success: false, error: 'URL is required' };
    }

    // Validate URL
    try {
      new URL(url);
    } catch (e) {
      return { success: false, error: `Invalid URL: ${url}` };
    }

    // This would normally use Puppeteer to scrape the website
    // Here we just simulate results

    return {
      success: true,
      message: `Website scraping simulation for ${url}`,
      url,
      selectors,
      pagination,
      outputFormat,
      results: [
        { title: "Sample Item 1", description: "Description for item 1" },
        { title: "Sample Item 2", description: "Description for item 2" }
      ],
      note: "This is a placeholder. In a real implementation, actual website scraping would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error scraping website: ${error.message}`
    };
  }
}

// Implement executeNaturalLanguageProcessing function
async function executeNaturalLanguageProcessing({ text, operation }) {
  try {
    if (!text) {
      return { success: false, error: 'Text is required' };
    }

    if (!operation) {
      return { success: false, error: 'Operation is required' };
    }

    // This would normally use NLP libraries or APIs
    // Here we just simulate results based on the operation

    let result;

    switch (operation.toLowerCase()) {
      case 'sentiment':
        result = {
          sentiment: text.length % 3 === 0 ? 'positive' : text.length % 3 === 1 ? 'neutral' : 'negative',
          score: ((text.length % 10) / 10).toFixed(2)
        };
        break;

      case 'entities':
        result = {
          entities: [
            { entity: 'example', type: 'ORGANIZATION', mentions: 1 },
            { entity: 'sample', type: 'CONCEPT', mentions: 2 }
          ]
        };
        break;

      case 'summarization':
        result = {
          summary: text.length > 100 ? text.substring(0, 100) + '...' : text
        };
        break;

      default:
        return { success: false, error: `Unsupported NLP operation: ${operation}` };
    }

    return {
      success: true,
      message: `NLP ${operation} operation completed`,
      operation,
      result,
      note: "This is a placeholder. In a real implementation, actual NLP processing would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error performing NLP operation: ${error.message}`
    };
  }
}

// Implement executeImageProcessing function
async function executeImageProcessing({ imagePath, operation, outputPath }) {
  try {
    if (!imagePath) {
      return { success: false, error: 'Image path is required' };
    }

    if (!fs.existsSync(imagePath)) {
      return { success: false, error: `Image does not exist: ${imagePath}` };
    }

    if (!operation) {
      return { success: false, error: 'Operation is required' };
    }

    // Determine output path if not provided
    const finalOutputPath = outputPath ||
                           path.join(
                             path.dirname(imagePath),
                             `${path.basename(imagePath, path.extname(imagePath))}_${operation}${path.extname(imagePath)}`
                           );

    // This would normally use image processing libraries
    // Here we just simulate by copying the image

    fs.copyFileSync(imagePath, finalOutputPath);

    return {
      success: true,
      message: `Image processing simulation for ${operation}`,
      imagePath,
      operation,
      outputPath: finalOutputPath,
      note: "This is a placeholder. In a real implementation, actual image processing would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error processing image: ${error.message}`
    };
  }
}

// Implement executeMachineLearningOperation function
async function executeMachineLearningOperation({ operation, modelType, dataPath, parameters = {} }) {
  try {
    if (!operation) {
      return { success: false, error: 'Operation is required' };
    }

    if (!modelType) {
      return { success: false, error: 'Model type is required' };
    }

    // This would normally use machine learning libraries or APIs
    // Here we just simulate results

    return {
      success: true,
      message: `Machine learning ${operation} simulation for ${modelType}`,
      operation,
      modelType,
      dataPath,
      parameters,
      results: {
        accuracy: 0.85,
        precision: 0.82,
        recall: 0.79,
        f1Score: 0.80
      },
      note: "This is a placeholder. In a real implementation, actual machine learning would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error performing machine learning operation: ${error.message}`
    };
  }
}

// Implement executeCloudResourceManagement function
async function executeCloudResourceManagement({ provider, resourceType, operation, parameters = {} }) {
  try {
    if (!provider) {
      return { success: false, error: 'Cloud provider is required' };
    }

    if (!resourceType) {
      return { success: false, error: 'Resource type is required' };
    }

    if (!operation) {
      return { success: false, error: 'Operation is required' };
    }

    // This would normally use cloud provider SDKs
    // Here we just simulate results

    return {
      success: true,
      message: `Cloud resource ${operation} simulation for ${provider} ${resourceType}`,
      provider,
      resourceType,
      operation,
      parameters,
      resourceId: `${resourceType}-${uuidv4().substring(0, 8)}`,
      note: "This is a placeholder. In a real implementation, actual cloud resources would be managed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error managing cloud resource: ${error.message}`
    };
  }
}

// Implement executeSecurityScan function
async function executeSecurityScan({ targetPath, scanType = 'vulnerability', severity = 'medium' }) {
  try {
    if (!targetPath) {
      return { success: false, error: 'Target path is required' };
    }

    if (!fs.existsSync(targetPath)) {
      return { success: false, error: `Target path does not exist: ${targetPath}` };
    }

    // This would normally use security scanning tools
    // Here we just simulate results

    return {
      success: true,
      message: `Security scan simulation for ${scanType}`,
      targetPath,
      scanType,
      severity,
      findings: [
        { id: 'VULN-001', type: 'dependency', severity: 'high', description: 'Example vulnerability' },
        { id: 'VULN-002', type: 'code', severity: 'medium', description: 'Example security issue' }
      ],
      summary: {
        high: 1,
        medium: 1,
        low: 0,
        total: 2
      },
      note: "This is a placeholder. In a real implementation, actual security scanning would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error performing security scan: ${error.message}`
    };
  }
}

// Implement executeDataTransformation function
async function executeDataTransformation({ inputPath, outputPath, inputFormat, outputFormat, transformations = [] }) {
  try {
    if (!inputPath) {
      return { success: false, error: 'Input path is required' };
    }

    if (!fs.existsSync(inputPath)) {
      return { success: false, error: `Input path does not exist: ${inputPath}` };
    }

    if (!outputPath) {
      return { success: false, error: 'Output path is required' };
    }

    // Determine formats if not provided
    const finalInputFormat = inputFormat || path.extname(inputPath).replace('.', '');
    const finalOutputFormat = outputFormat || path.extname(outputPath).replace('.', '');

    // This would normally use data transformation libraries
    // Here we just copy the file with a note

    // Create output directory if it doesn't exist
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // For transformation simulation, create a simple output file
    fs.writeFileSync(
      outputPath,
      `This is a simulated transformed file.\nOriginal: ${inputPath} (${finalInputFormat})\nTransformed to: ${outputFormat}`,
      'utf8'
    );

    return {
      success: true,
      message: `Data transformation simulation from ${finalInputFormat} to ${finalOutputFormat}`,
      inputPath,
      outputPath,
      inputFormat: finalInputFormat,
      outputFormat: finalOutputFormat,
      transformations,
      note: "This is a placeholder. In a real implementation, actual data transformation would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error transforming data: ${error.message}`
    };
  }
}

// Implement executeMobileAppDevelopment function
async function executeMobileAppDevelopment({ platform, projectPath, operation, parameters = {} }) {
  try {
    if (!platform) {
      return { success: false, error: 'Mobile platform is required' };
    }

    if (!operation) {
      return { success: false, error: 'Operation is required' };
    }

    // Handle different operations
    switch (operation.toLowerCase()) {
      case 'create':
        // Create a new mobile app project
        if (!projectPath) {
          return { success: false, error: 'Project path is required for create operation' };
        }

        if (fs.existsSync(projectPath)) {
          return { success: false, error: `Project directory already exists: ${projectPath}` };
        }

        // Create directory
        fs.mkdirSync(projectPath, { recursive: true });

        // Create basic project structure based on platform
        switch (platform.toLowerCase()) {
          case 'react-native':
            return {
              success: true,
              message: `React Native project created at ${projectPath}`,
              nextSteps: [
                `cd ${projectPath}`,
                `npx react-native init MyApp`
              ],
              note: "This is a placeholder. In a real implementation, a full React Native project would be created."
            };

          case 'flutter':
            return {
              success: true,
              message: `Flutter project created at ${projectPath}`,
              nextSteps: [
                `cd ${projectPath}`,
                `flutter create my_app`
              ],
              note: "This is a placeholder. In a real implementation, a full Flutter project would be created."
            };

          default:
            return { success: false, error: `Unsupported mobile platform: ${platform}` };
        }

      case 'build':
        // Build an existing mobile app project
        if (!projectPath) {
          return { success: false, error: 'Project path is required for build operation' };
        }

        if (!fs.existsSync(projectPath)) {
          return { success: false, error: `Project directory does not exist: ${projectPath}` };
        }

        return {
          success: true,
          message: `${platform} build simulation`,
          platform,
          projectPath,
          note: "This is a placeholder. In a real implementation, the mobile app would be built."
        };

      default:
        return { success: false, error: `Unsupported operation: ${operation}` };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error in mobile app development: ${error.message}`
    };
  }
}

// Implement executeBlockchainOperation function
async function executeBlockchainOperation({ blockchain, operation, parameters = {} }) {
  try {
    if (!blockchain) {
      return { success: false, error: 'Blockchain is required' };
    }

    if (!operation) {
      return { success: false, error: 'Operation is required' };
    }

    // This would normally use blockchain SDKs or APIs
    // Here we just simulate results

    return {
      success: true,
      message: `Blockchain ${operation} simulation for ${blockchain}`,
      blockchain,
      operation,
      parameters,
      transactionId: `0x${uuidv4().replace(/-/g, '')}`,
      note: "This is a placeholder. In a real implementation, actual blockchain operations would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error in blockchain operation: ${error.message}`
    };
  }
}

// Implement executeIoTDeviceManagement function
async function executeIoTDeviceManagement({ deviceId, operation, parameters = {} }) {
  try {
    if (!deviceId) {
      return { success: false, error: 'Device ID is required' };
    }

    if (!operation) {
      return { success: false, error: 'Operation is required' };
    }

    // This would normally use IoT platform SDKs or APIs
    // Here we just simulate results

    return {
      success: true,
      message: `IoT device ${operation} simulation for device ${deviceId}`,
      deviceId,
      operation,
      parameters,
      status: 'success',
      note: "This is a placeholder. In a real implementation, actual IoT device management would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error in IoT device management: ${error.message}`
    };
  }
}

// The importOpen function is already defined above

// Program initialization
program
  .version('1.0.0')
  .description('Gemini Advanced Agent - An AI-powered command-line assistant')
  .option('-i, --interactive', 'Start in interactive mode')
  .parse(process.argv);

const options = program.opts();

// Process command line options
function processOptions() {
  // Start in interactive mode by default or if explicitly requested
  if (options.interactive || process.argv.length <= 2) {
    interactiveMode();
    return;
  }

  // If we get here, we're not in interactive mode
  console.log(chalk.green('=== Gemini Advanced Agent ==='));
  console.log(chalk.yellow('Running in non-interactive mode'));

  // In the future, we could add support for running specific commands
  // For now, just fall back to interactive mode
  interactiveMode();
}

// Start the agent
processOptions();
