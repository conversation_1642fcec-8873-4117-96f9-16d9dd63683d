{"version": 3, "file": "constant.js", "sources": ["../../../src/js/constant.ts"], "sourcesContent": ["/**\n * constant\n */\n\n/* values and units */\nconst _DIGIT = '(?:0|[1-9]\\\\d*)';\nconst _COMPARE = 'clamp|max|min';\nconst _EXPO = 'exp|hypot|log|pow|sqrt';\nconst _SIGN = 'abs|sign';\nconst _STEP = 'mod|rem|round';\nconst _TRIG = 'a?(?:cos|sin|tan)|atan2';\nconst _MATH = `${_COMPARE}|${_EXPO}|${_SIGN}|${_STEP}|${_TRIG}`;\nconst _CALC = `calc|${_MATH}`;\nconst _VAR = `var|${_CALC}`;\nexport const ANGLE = 'deg|g?rad|turn';\nexport const LENGTH =\n  '[cm]m|[dls]?v(?:[bhiw]|max|min)|in|p[ctx]|q|r?(?:[cl]h|cap|e[mx]|ic)';\nexport const NUM = `[+-]?(?:${_DIGIT}(?:\\\\.\\\\d*)?|\\\\.\\\\d+)(?:e-?${_DIGIT})?`;\nexport const NUM_POSITIVE = `\\\\+?(?:${_DIGIT}(?:\\\\.\\\\d*)?|\\\\.\\\\d+)(?:e-?${_DIGIT})?`;\nexport const NONE = 'none';\nexport const PCT = `${NUM}%`;\nexport const SYN_FN_CALC = `^(?:${_CALC})\\\\(|(?<=[*\\\\/\\\\s\\\\(])(?:${_CALC})\\\\(`;\nexport const SYN_FN_MATH_START = `^(?:${_MATH})\\\\($`;\nexport const SYN_FN_VAR = '^var\\\\(|(?<=[*\\\\/\\\\s\\\\(])var\\\\(';\nexport const SYN_FN_VAR_START = `^(?:${_VAR})\\\\(`;\n\n/* colors */\nconst _ALPHA = `(?:\\\\s*\\\\/\\\\s*(?:${NUM}|${PCT}|${NONE}))?`;\nconst _ALPHA_LV3 = `(?:\\\\s*,\\\\s*(?:${NUM}|${PCT}))?`;\nconst _COLOR_FUNC = '(?:ok)?l(?:ab|ch)|color|hsla?|hwb|rgba?';\nconst _COLOR_KEY = '[a-z]+|#[\\\\da-f]{3}|#[\\\\da-f]{4}|#[\\\\da-f]{6}|#[\\\\da-f]{8}';\nconst _CS_HUE = '(?:ok)?lch|hsl|hwb';\nconst _CS_HUE_ARC = '(?:de|in)creasing|longer|shorter';\nconst _NUM_ANGLE = `${NUM}(?:${ANGLE})?`;\nconst _NUM_ANGLE_NONE = `(?:${NUM}(?:${ANGLE})?|${NONE})`;\nconst _NUM_PCT_NONE = `(?:${NUM}|${PCT}|${NONE})`;\nexport const CS_HUE = `(?:${_CS_HUE})(?:\\\\s(?:${_CS_HUE_ARC})\\\\shue)?`;\nexport const CS_HUE_CAPT = `(${_CS_HUE})(?:\\\\s(${_CS_HUE_ARC})\\\\shue)?`;\nexport const CS_LAB = '(?:ok)?lab';\nexport const CS_LCH = '(?:ok)?lch';\nexport const CS_SRGB = 'srgb(?:-linear)?';\nexport const CS_RGB = `(?:a98|prophoto)-rgb|display-p3|rec2020|${CS_SRGB}`;\nexport const CS_XYZ = 'xyz(?:-d(?:50|65))?';\nexport const CS_RECT = `${CS_LAB}|${CS_RGB}|${CS_XYZ}`;\nexport const CS_MIX = `${CS_HUE}|${CS_RECT}`;\nexport const FN_COLOR = 'color(';\nexport const FN_MIX = 'color-mix(';\nexport const FN_REL = `(?:${_COLOR_FUNC})\\\\(\\\\s*from\\\\s+`;\nexport const FN_REL_CAPT = `(${_COLOR_FUNC})\\\\(\\\\s*from\\\\s+`;\nexport const FN_VAR = 'var(';\nexport const SYN_FN_COLOR = `(?:${CS_RGB}|${CS_XYZ})(?:\\\\s+${_NUM_PCT_NONE}){3}${_ALPHA}`;\nexport const SYN_FN_REL = `^${FN_REL}|(?<=[\\\\s])${FN_REL}`;\nexport const SYN_HSL = `${_NUM_ANGLE_NONE}(?:\\\\s+${_NUM_PCT_NONE}){2}${_ALPHA}`;\nexport const SYN_HSL_LV3 = `${_NUM_ANGLE}(?:\\\\s*,\\\\s*${PCT}){2}${_ALPHA_LV3}`;\nexport const SYN_LCH = `(?:${_NUM_PCT_NONE}\\\\s+){2}${_NUM_ANGLE_NONE}${_ALPHA}`;\nexport const SYN_MOD = `${_NUM_PCT_NONE}(?:\\\\s+${_NUM_PCT_NONE}){2}${_ALPHA}`;\nexport const SYN_RGB_LV3 = `(?:${NUM}(?:\\\\s*,\\\\s*${NUM}){2}|${PCT}(?:\\\\s*,\\\\s*${PCT}){2})${_ALPHA_LV3}`;\nexport const SYN_COLOR_TYPE = `${_COLOR_KEY}|hsla?\\\\(\\\\s*${SYN_HSL_LV3}\\\\s*\\\\)|rgba?\\\\(\\\\s*${SYN_RGB_LV3}\\\\s*\\\\)|(?:hsla?|hwb)\\\\(\\\\s*${SYN_HSL}\\\\s*\\\\)|(?:(?:ok)?lab|rgba?)\\\\(\\\\s*${SYN_MOD}\\\\s*\\\\)|(?:ok)?lch\\\\(\\\\s*${SYN_LCH}\\\\s*\\\\)|color\\\\(\\\\s*${SYN_FN_COLOR}\\\\s*\\\\)`;\nexport const SYN_MIX_PART = `(?:${SYN_COLOR_TYPE})(?:\\\\s+${PCT})?`;\nexport const SYN_MIX = `color-mix\\\\(\\\\s*in\\\\s+(?:${CS_MIX})\\\\s*,\\\\s*${SYN_MIX_PART}\\\\s*,\\\\s*${SYN_MIX_PART}\\\\s*\\\\)`;\nexport const SYN_MIX_CAPT = `color-mix\\\\(\\\\s*in\\\\s+(${CS_MIX})\\\\s*,\\\\s*(${SYN_MIX_PART})\\\\s*,\\\\s*(${SYN_MIX_PART})\\\\s*\\\\)`;\n\n/* formats */\nexport const VAL_COMP = 'computedValue';\nexport const VAL_MIX = 'mixValue';\nexport const VAL_SPEC = 'specifiedValue';\n"], "names": [], "mappings": "AAKA,MAAM,SAAS;AACf,MAAM,WAAW;AACjB,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ,GAAG,QAAQ,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AAC7D,MAAM,QAAQ,QAAQ,KAAK;AAC3B,MAAM,OAAO,OAAO,KAAK;AAClB,MAAM,QAAQ;AACd,MAAM,SACX;AACK,MAAM,MAAM,WAAW,MAAM,8BAA8B,MAAM;AACjE,MAAM,eAAe,UAAU,MAAM,8BAA8B,MAAM;AACzE,MAAM,OAAO;AACP,MAAA,MAAM,GAAG,GAAG;AAClB,MAAM,cAAc,OAAO,KAAK,4BAA4B,KAAK;AAC3D,MAAA,oBAAoB,OAAO,KAAK;AACtC,MAAM,aAAa;AACb,MAAA,mBAAmB,OAAO,IAAI;AAG3C,MAAM,SAAS,oBAAoB,GAAG,IAAI,GAAG,IAAI,IAAI;AACrD,MAAM,aAAa,kBAAkB,GAAG,IAAI,GAAG;AAC/C,MAAM,cAAc;AACpB,MAAM,aAAa;AACnB,MAAM,UAAU;AAChB,MAAM,cAAc;AACpB,MAAM,aAAa,GAAG,GAAG,MAAM,KAAK;AACpC,MAAM,kBAAkB,MAAM,GAAG,MAAM,KAAK,MAAM,IAAI;AACtD,MAAM,gBAAgB,MAAM,GAAG,IAAI,GAAG,IAAI,IAAI;AACvC,MAAM,SAAS,MAAM,OAAO,aAAa,WAAW;AACpD,MAAM,cAAc,IAAI,OAAO,WAAW,WAAW;AACrD,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,UAAU;AACV,MAAA,SAAS,2CAA2C,OAAO;AACjE,MAAM,SAAS;AACf,MAAM,UAAU,GAAG,MAAM,IAAI,MAAM,IAAI,MAAM;AAC7C,MAAM,SAAS,GAAG,MAAM,IAAI,OAAO;AACnC,MAAM,WAAW;AACjB,MAAM,SAAS;AACT,MAAA,SAAS,MAAM,WAAW;AAC1B,MAAA,cAAc,IAAI,WAAW;AACnC,MAAM,SAAS;AACT,MAAA,eAAe,MAAM,MAAM,IAAI,MAAM,WAAW,aAAa,OAAO,MAAM;AAChF,MAAM,aAAa,IAAI,MAAM,cAAc,MAAM;AACjD,MAAM,UAAU,GAAG,eAAe,UAAU,aAAa,OAAO,MAAM;AACtE,MAAM,cAAc,GAAG,UAAU,eAAe,GAAG,OAAO,UAAU;AACpE,MAAM,UAAU,MAAM,aAAa,WAAW,eAAe,GAAG,MAAM;AACtE,MAAM,UAAU,GAAG,aAAa,UAAU,aAAa,OAAO,MAAM;AAC9D,MAAA,cAAc,MAAM,GAAG,eAAe,GAAG,QAAQ,GAAG,eAAe,GAAG,QAAQ,UAAU;AAC9F,MAAM,iBAAiB,GAAG,UAAU,gBAAgB,WAAW,uBAAuB,WAAW,+BAA+B,OAAO,sCAAsC,OAAO,4BAA4B,OAAO,uBAAuB,YAAY;AAC1P,MAAM,eAAe,MAAM,cAAc,WAAW,GAAG;AACvD,MAAM,UAAU,4BAA4B,MAAM,aAAa,YAAY,YAAY,YAAY;AACnG,MAAM,eAAe,0BAA0B,MAAM,cAAc,YAAY,cAAc,YAAY;AAGzG,MAAM,WAAW;AACjB,MAAM,UAAU;AAChB,MAAM,WAAW;"}